{% extends "base.html" %} {% block title %}Vacancies{% endblock %} {% block
content %}
<div class="less-limited-width-content">
  <div
    id="search-div"
    class="modern-card rounded-3 p-4 mb-4 shadow-sm"
  >
    <div class="text-center mb-4">
      <h2 class="h4 fw-bold text-primary mb-2">
        <i class="bi bi-search me-2"></i>Find Your Dream Job
      </h2>
      <p class="text-muted mb-0">Search through thousands of opportunities</p>
    </div>

    <div class="row g-3">
      <div class="col-12 col-md-5">
        <div class="form-floating">
          <input
            class="form-control border-2"
            id="keyword-input"
            name="keyword-input"
            placeholder=""
            hx-get="/filterjobs"
            hx-target="#results-div"
            hx-trigger="keyup changed delay:300ms"
            hx-include="#location-input, #remote-toggle"
            hx-indicator="#search-loading"
          />
          <label for="keyword-input">
            <i class="bi bi-briefcase me-2"></i>Job Title, Skills, or Keywords
          </label>
        </div>
      </div>
      <div class="col-12 col-md-5">
        <div class="form-floating position-relative">
          <input
            class="form-control border-2"
            id="location-input"
            name="location-input"
            placeholder=""
            autocomplete="off"
            hx-get="/filterjobs"
            hx-target="#results-div"
            hx-trigger="keyup changed delay:300ms"
            hx-include="#keyword-input, #remote-toggle"
            hx-indicator="#search-loading"
          />
          <label for="location-input">
            <i class="bi bi-geo-alt me-2"></i>Location (City, Country)
          </label>
          <div id="location-suggestions" class="location-dropdown"></div>
        </div>
      </div>
      <div class="col-12 col-md-2">
        <div class="d-flex align-items-center h-100">
          <div class="form-check form-switch">
            <input
              class="form-check-input"
              type="checkbox"
              id="remote-toggle"
              name="remote-toggle"
              hx-get="/filterjobs"
              hx-target="#results-div"
              hx-trigger="change"
              hx-include="#keyword-input, #location-input"
              hx-indicator="#search-loading"
              checked
            >
            <label class="form-check-label fw-semibold text-primary" for="remote-toggle">
              <i class="bi bi-laptop me-1"></i>Include Remote
            </label>
          </div>
        </div>
      </div>
    </div>

    <div class="text-center mt-3">
      <div id="search-loading" class="htmx-indicator">
        <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <span class="text-muted">Searching jobs...</span>
      </div>
    </div>
  </div>

  <div id="results-div" class="mt-4 row">
    <!-- Jobs List Column -->
<div class="col-12 col-md-4 col-lg-3 mb-4 mb-md-0">
  <div
    id="jobs-small-list"
    class="rounded py-4 px-2 row shadow-sm mx-0 modern-card"
  >
    <div class="d-flex align-items-center justify-content-between mb-3">
      <h5 class="mb-0 fw-bold text-primary">
        <i class="bi bi-briefcase me-2"></i>Job Listings
      </h5>
      <span class="badge bg-primary rounded-pill">{{all_vacancies|length}}</span>
    </div>
    <hr class="my-3" />
    <div
      id="sorted-jobs-short"
      class="overflow-auto"
      style="max-height: 65vh;"
    >
      <div id="jobs-listed">
        {% if all_vacancies %}
        {% for vacancy in all_vacancies %}
        <div class="listing-item mb-3">
          <div
            id="button-{{vacancy.vacancy_id}}"
            class="job-card-mini p-3 rounded-3 listing-pointer {% if loop.first %} active {% endif %}"
            onclick="switch_listing('button-{{vacancy.vacancy_id}}','listing-div-{{vacancy.vacancy_id}}')"
          >
            <div class="d-flex align-items-start">
              <div class="flex-shrink-0 me-3">
                {% if vacancy.employer_logo_url %}
                <img 
                  src="{{vacancy.employer_logo_url}}"
                  class="img-fluid rounded employer-icon-sm"
                  alt="{{vacancy.employer_name}} logo"
                  style="max-width: 40px;"
                  onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                />
                {% endif %}
                <div class="company-logo-fallback" style="display: {% if not vacancy.employer_logo_url %}flex{% else %}none{% endif %}; align-items: center; justify-content: center; width: 40px; height: 40px; background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%); border-radius: 8px; color: white; font-weight: 600; font-size: 1rem;">
                  {{ (vacancy.employer_name or 'C')[0]|upper }}
                </div>
              </div>
              <div class="flex-grow-1 min-w-0">
                <p class="mb-1 fw-semibold">{{vacancy.vacancy_title}}</p>
                <p class="mb-1 text-muted small fw-medium">{{vacancy.employer_name}}</p>
                <div class="d-flex align-items-center text-muted small">
                  <i class="bi bi-geo-alt me-1"></i>
                  <span class="text-truncate">
                    {% if vacancy.vacancy_city %}{{vacancy.vacancy_city}}, {% endif %}{{vacancy.vacancy_country}}
                  </span>
                </div>
                {% if vacancy.listing_source == 'WeWorkRemotely' %}
                <span class="badge bg-success bg-opacity-10 small mt-1">
                  <i class="bi bi-rss me-1"></i>Remote
                </span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
        {% endfor %}
        {% else %}
        <div class="text-center py-5">
          <i class="bi bi-search display-4 text-muted mb-3"></i>
          <h5 class="text-muted">No jobs found</h5>
          <p class="text-muted small">Try adjusting your search criteria</p>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<!-- Job Details Column -->
<div class="col-12 col-md-8 col-lg-9">
  {% if all_vacancies %}
  {% for vacancy in all_vacancies %}
  <div
    id="listing-div-{{vacancy.vacancy_id}}"
    class="modern-card rounded-3 listing shadow-sm {% if loop.first %} visible {% endif %}"
  >
    <!-- Modern Header with Gradient -->
    <div
      class="rounded-top-3 w-100 position-relative"
      style="
        height: 140px;
        background: {% if vacancy.listing_source == 'WeWorkRemotely' %}
          linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        {% else %}
          linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
        {% endif %}
        {% if vacancy.employer_banner_url %}
          background-image: url('{{vacancy.employer_banner_url}}');
          background-size: cover;
          background-position: center;
          background-blend-mode: overlay;
        {% endif %}
      "
    >
      <div class="position-absolute top-0 end-0 p-3">
        {% if vacancy.listing_source == 'WeWorkRemotely' %}
        <span class="badge bg-light text-dark">
          <i class="bi bi-rss me-1"></i>Remote Job
        </span>
        {% endif %}
      </div>
    </div>

    <div class="p-4">
      <!-- Modern Job Header -->
      <div class="d-flex align-items-start mb-4">
        <div class="flex-shrink-0 me-4">
          {% if vacancy.employer_logo_url %}
          <img
            src="{{vacancy.employer_logo_url}}"
            class="rounded-3 shadow-sm"
            alt="{{vacancy.employer_name}} logo"
            style="width: 80px; height: 80px; object-fit: stretch;"
            onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
          />
          {% endif %}
          <div class="company-logo-fallback" style="display: {% if not vacancy.employer_logo_url %}flex{% else %}none{% endif %}; align-items: center; justify-content: center; width: 80px; height: 80px; background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%); border-radius: 12px; color: white; font-weight: 600; font-size: 2rem;">
            {{ (vacancy.employer_name or 'C')[0]|upper }}
          </div>
        </div>
        <div class="flex-grow-1">
          <h2 class="h3 fw-bold mb-2 text-dark">{{vacancy.vacancy_title}}</h2>
          <div class="d-flex align-items-center mb-2">
            <i class="bi bi-building text-primary me-2"></i>
            <span class="fw-semibold text-muted">{{vacancy.employer_name}}</span>
          </div>
          <div class="d-flex align-items-center text-muted">
            <i class="bi bi-geo-alt text-primary me-2"></i>
            <span>
              {% if vacancy.vacancy_city %}{{vacancy.vacancy_city}}, {% endif %}{{vacancy.vacancy_country}}
            </span>
          </div>
        </div>
      </div>

      <div class="row g-4">
        <!-- Job Description Section -->
        <div class="col-12 col-lg-8">
          <div class="job-description-content">
            {{ vacancy.vacancy_job_description|safe }}
          </div>
        </div>

        <!-- Modern Job Details Sidebar -->
        <div class="col-12 col-lg-4">
          <div class="job-sidebar bg-light rounded-3 p-4">
            <!-- Position Facts -->
            <div class="mb-4">
              <h6 class="fw-bold text-primary mb-3">
                <i class="bi bi-info-circle me-2"></i>Position Details
              </h6>
              <div class="fact-item mb-3">
                <div class="d-flex align-items-center mb-1">
                  <i class="bi bi-currency-dollar text-dark me-2"></i>
                  <span class="small fw-semibold text-muted">Salary Range</span>
                </div>
                <div class="ms-4">
                  {% if vacancy.salary_min and vacancy.salary_max %}
                    {{vacancy.salary_min}} - {{vacancy.salary_max}} {{vacancy.salary_currency}}
                  {% else %}
                    Not specified
                  {% endif %}
                </div>
              </div>

              <div class="fact-item mb-3">
                <div class="d-flex align-items-center mb-1">
                  <i class="bi bi-laptop text-dark me-2"></i>
                  <span class="small fw-semibold text-muted">Work Mode</span>
                </div>
                <div class="ms-4">
                  {% if vacancy.office_schedule %}
                    {{vacancy.office_schedule}}
                  {% else %}
                    Not specified
                  {% endif %}
                </div>
              </div>

              <div class="fact-item">
                <div class="d-flex align-items-center mb-1">
                  <i class="bi bi-geo-alt text-dark me-2"></i>
                  <span class="small fw-semibold text-muted">Location</span>
                </div>
                <div class="ms-4">
                  {% if vacancy.vacancy_city %}{{vacancy.vacancy_city}}, {% endif %}{{vacancy.vacancy_country}}
                </div>
              </div>
            </div>

            {% if all_tags.get(vacancy.vacancy_id) and all_tags[vacancy.vacancy_id][0] %}
            <!-- Key Advantages -->
            <div class="mb-4">
              <h6 class="fw-bold text-primary mb-3">
                <i class="bi bi-star me-2"></i>Key Benefits
              </h6>
              <div class="d-flex flex-wrap gap-2">
                {% for tag in all_tags[vacancy.vacancy_id] %}
                <span class="badge bg-dark bg-opacity-10 text-light border border-dark border-opacity-25 px-3 py-2">
                  {{tag}}
                </span>
                {% endfor %}
              </div>
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="d-grid gap-3">
              {% if vacancy.listing_source == "Canvider" %}
              <button
                type="button"
                class="btn btn-primary btn-lg shadow-sm"
                data-bs-toggle="modal"
                data-bs-target="#applicationModal-{{vacancy.vacancy_id}}"
              >
                <i class="bi bi-send me-2"></i>Apply Now
              </button>
              <a
                href="{{url_for('employer',employer_id=vacancy.employer_id)}}"
                target="_blank"
                class="btn btn-outline-primary btn-lg shadow-sm"
              >
                <i class="bi bi-building me-2"></i>View Company
              </a>
              {% else %}
              <a
                href="{{vacancy.vacancy_url}}"
                target="_blank"
                class="btn btn-primary btn-lg shadow-sm"
              >
                <i class="bi bi-send me-2"></i>Apply Now
              </a>
              <a
                href="{{vacancy.company_url}}"
                target="_blank"
                class="btn btn-outline-primary btn-lg shadow-sm"
              >
                <i class="bi bi-building me-2"></i>View Company
              </a>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Application Modal for each vacancy -->
  {% if vacancy.listing_source == "Canvider" %}
  <div class="modal fade" id="applicationModal-{{vacancy.vacancy_id}}" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h2 class="modal-title h5">Quick Application</h2>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
          ></button>
        </div>
        <div class="modal-body">
          <form action="/submit" enctype="multipart/form-data" method="post">
            <div class="mb-3">
              <label class="form-label">Position ID</label>
              <input
                name="vac_id"
                value="{{vacancy.vacancy_id}}"
                class="form-control"
                readonly
                required
              />
            </div>

            <div class="mb-3">
              <label class="form-label">Position Title</label>
              <input
                value="{{vacancy.vacancy_title}}"
                class="form-control"
                name="vac_title"
                disabled
                required
              />
            </div>

            <input
                value="jobs_list"
                name="source"
                type="hidden"
                required
              />

            <div class="mb-3 row">
              <div class="col-6">
                <label class="form-label">First Name</label>
                <input
                  name="fname"
                  type="text"
                  class="form-control"
                  placeholder="John"
                  required
                />
              </div>
              <div class="col-6">
                <label class="form-label">Last Name</label>
                <input
                  name="lname"
                  type="text"
                  class="form-control"
                  placeholder="Doe"
                  required
                />
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label">Email</label>
              <input
                name="email"
                type="email"
                class="form-control"
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div class="mb-3">
              <label class="form-label">Phone</label>
              <input
                name="phone"
                type="tel"
                class="form-control"
                placeholder="+1234567890"
                pattern="^\+?[1-9]\d{1,14}$"
                required
              />
            </div>

            <div class="mb-3">
              <label class="form-label">Notice Period</label>
              <select name="notice" class="form-select" required>
                <option>Ready immediately</option>
                <option>2 weeks</option>
                <option>1 month</option>
                <option>2 months</option>
                <option>3 months</option>
                <option>More than 3 months</option>
              </select>
            </div>

            <div class="mb-4">
              <label class="form-label">Upload CV (PDF only)</label>
              <input
                name="cv"
                type="file"
                class="form-control"
                accept=".pdf"
                onchange="validateFileSize(this)"
                required
              />
            </div>

            <div class="d-grid">
              <button type="submit" class="btn btn-primary btn-lg">
                Submit Application
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
  {% endif %}
  {% endfor %}
  {% endif %}
</div>

  </div>
</div>

<style>
  /* Fix for mobile vertical gap issue */
  .listing {
    display: none;
  }

  .listing.visible {
    display: block;
  }

  .listing-pointer {
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .listing-pointer:hover {
    background-color: #f8f9fa;
  }

  .listing-pointer.active {
    background-color: #e3f2fd;
    border-left: 4px solid var(--bs-primary);
  }

  @media (max-width: 768px) {
    #jobs-small-list {
      margin: 0 0.5rem;
    }

    .listing-item {
      padding: 0.5rem;
    }

    .employer-icon-sm {
      max-width: 30px;
    }

    #search-div {
      margin: 0 0.5rem;
    }

    .listing {
      margin-top: 1rem;
      /* Fix mobile gap issue */
      position: relative;
      min-height: auto;
      overflow: hidden;
    }

    /* Ensure proper spacing on mobile */
    .col-12.col-md-8.col-lg-9 {
      padding-left: 0.75rem;
      padding-right: 0.75rem;
    }

    /* Fix for vertical gap when switching jobs on mobile */
    .modern-card.listing {
      margin-bottom: 0;
      transition: none;
    }

    .modern-card.listing:not(.visible) {
      display: none !important;
      height: 0;
      margin: 0;
      padding: 0;
      overflow: hidden;
    }

    .border-lg-end {
      border-right: none !important;
    }
  }

  @media (max-width: 576px) {
    .employer-icon-md {
      max-width: 60px;
    }

    h3 {
      font-size: 1.25rem;
    }

    .h5 {
      font-size: 1.1rem;
    }

    .badge {
      font-size: 0.8rem;
    }

    /* Additional mobile fixes */
    .less-limited-width-content {
      padding: 0 0.5rem;
    }

    .row.g-3 {
      margin: 0;
    }

    .col-12.col-md-4.col-lg-3 {
      padding: 0 0.5rem;
    }
  }
</style>


<script>
  function validateFileSize(input) {
    const file = input.files[0];
    if (file) {
      const fileSizeInMB = file.size / 1024 / 1024;
      if (fileSizeInMB > 3) {
        alert("File size must be less than 3MB");
        input.value = "";
      } else if (file.type !== "application/pdf") {
        alert("File must be a PDF");
        input.value = "";
      }
    }
  }

  // Add a small loading animation to the submit button
  document.addEventListener("DOMContentLoaded", function () {
    const submitButtons = document.querySelectorAll(".btn-primary[type='submit']");
    submitButtons.forEach(button => {
      button.addEventListener("click", function () {
        this.innerHTML = "Submitting...";
      });
    });
  });
</script>


<script>
  // Location autocomplete functionality
  document.addEventListener('DOMContentLoaded', function() {
    const locationInput = document.getElementById('location-input');
    const suggestionsDiv = document.getElementById('location-suggestions');
    let debounceTimer;

    locationInput.addEventListener('input', function() {
      clearTimeout(debounceTimer);
      const query = this.value.trim();

      if (query.length < 2) {
        suggestionsDiv.style.display = 'none';
        return;
      }

      debounceTimer = setTimeout(() => {
        fetch(`/search-locations?q=${encodeURIComponent(query)}`)
          .then(response => response.json())
          .then(data => {
            if (data.locations && data.locations.length > 0) {
              showSuggestions(data.locations);
            } else {
              suggestionsDiv.style.display = 'none';
            }
          })
          .catch(error => {
            console.error('Error fetching locations:', error);
            suggestionsDiv.style.display = 'none';
          });
      }, 200);
    });

    function showSuggestions(locations) {
      suggestionsDiv.innerHTML = '';
      locations.forEach(location => {
        const item = document.createElement('div');
        item.className = 'location-suggestion-item';
        item.textContent = location;
        item.addEventListener('click', function() {
          locationInput.value = location;
          suggestionsDiv.style.display = 'none';
          // Trigger the search
          htmx.trigger(locationInput, 'keyup');
        });
        suggestionsDiv.appendChild(item);
      });
      suggestionsDiv.style.display = 'block';
    }

    // Hide suggestions when clicking outside
    document.addEventListener('click', function(event) {
      if (!locationInput.contains(event.target) && !suggestionsDiv.contains(event.target)) {
        suggestionsDiv.style.display = 'none';
      }
    });
  });

  function switch_listing(btnId, targetId) {
    // Hide all listings first
    document.querySelectorAll(".listing").forEach((content) => {
      content.classList.remove("visible");
      // Force hide on mobile to prevent gaps
      if (window.innerWidth < 768) {
        content.style.display = "none";
      }
    });

    // Show target listing
    const targetDiv = document.getElementById(targetId);
    if (targetDiv) {
      targetDiv.classList.add("visible");
      if (window.innerWidth < 768) {
        targetDiv.style.display = "block";
      }
    }

    // Update active states
    document.querySelectorAll(".listing-pointer").forEach((content) => {
      content.classList.remove("active");
    });

    const sourceDiv = document.getElementById(btnId);
    if (sourceDiv) sourceDiv.classList.add("active");

    // Scroll to top of listing on mobile with better positioning
    if (window.innerWidth < 768 && targetDiv) {
      // Small delay to ensure display changes are applied
      setTimeout(() => {
        targetDiv.scrollIntoView({
          behavior: "smooth",
          block: "start",
          inline: "nearest"
        });
      }, 50);
    }
  }

  // Handle URL parameters for search functionality
  document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const keywordParam = urlParams.get('keyword-input');

    if (keywordParam) {
      const keywordInput = document.getElementById('keyword-input');
      if (keywordInput) {
        keywordInput.value = keywordParam;
        // Trigger the search automatically
        setTimeout(() => {
          htmx.trigger(keywordInput, 'keyup');
        }, 100);
      }
    }
  });


</script>

{% endblock %}
