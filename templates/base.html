<!DOCTYPE html>
<html lang="en">
  <head>
    <title>workloupe</title>
    <!-- Required meta tags -->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <link
      rel="icon"
      type="image/png"
      href="{{ url_for('static', filename='favicon-96x96.png') }}"
      sizes="96x96"
    />
    <link
      rel="icon"
      type="image/svg+xml"
      href="{{ url_for('static', filename='favicon.svg') }}"
    />
    <link
      rel="shortcut icon"
      href="{{ url_for('static', filename='favicon.ico') }}"
    />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="{{ url_for('static', filename='apple-touch-icon.png') }}"
    />
    <meta name="apple-mobile-web-app-title" content="WorkLoupe" />
    <link
      rel="manifest"
      href="{{ url_for('static', filename='site.webmanifest') }}"
    />

    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC"
      crossorigin="anonymous"
    />
    <script
      src="{{ url_for('static',filename='htmx.min.js') }}"
      type="text/javascript"
    ></script>
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.min.css"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="{{ url_for('static',filename='style.css') }}"
    />

    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM"
      crossorigin="anonymous"
    ></script>

    <!-- Dark Theme Script -->
    <script>
      // Theme management
      const themeToggle = document.getElementById('theme-toggle');
      const themeIcon = document.getElementById('theme-icon');
      const themeText = document.getElementById('theme-text');
      const html = document.documentElement;

      // Check for saved theme preference or default to 'light'
      const currentTheme = localStorage.getItem('theme') ||
        (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');

      // Apply theme on page load
      function applyTheme(theme) {
        if (theme === 'dark') {
          html.setAttribute('data-theme', 'dark');
          themeIcon.className = 'bi bi-sun-fill';
          themeText.textContent = 'Light';
        } else {
          html.removeAttribute('data-theme');
          themeIcon.className = 'bi bi-moon-fill';
          themeText.textContent = 'Dark';
        }
      }

      // Initialize theme
      applyTheme(currentTheme);

      // Theme toggle functionality
      if (themeToggle) {
        themeToggle.addEventListener('click', () => {
          const currentTheme = html.getAttribute('data-theme');
          const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

          applyTheme(newTheme);
          localStorage.setItem('theme', newTheme);
        });
      }

      // Listen for system theme changes
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (!localStorage.getItem('theme')) {
          applyTheme(e.matches ? 'dark' : 'light');
        }
      });
    </script>
  </head>

  <body style="background-color: #fcfcfc">
    <nav
      class="navbar navbar-expand-md navbar-light bg-light sticky-top shadow-sm"
    >
      <div class="container-fluid less-limited-width-content py-4">
        <a class="navbar-brand mx-5" href="/">
          <img
            src="{{ url_for('static',filename='logo.svg') }}"
            alt=""
            width="24"
            height="24"
            class="d-inline-block align-text-top me-1 text-primary"
          />
          workloupe
        </a>
        <!-- Add data-bs-toggle and data-bs-target attributes to the button -->
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarScroll"
          aria-controls="navbarScroll"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <!-- Add the navbar-toggler-icon class for the hamburger icon -->
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarScroll">
          <ul class="navbar-nav ms-auto my-2 my-lg-0 navbar-nav-scroll">
            <li class="nav-item mx-3">
              <a class="nav-link btn" href="/jobs">Jobs</a>
            </li>
            <li class="nav-item mx-3">
              <a class="nav-link btn" href="/job-alerts">
                <i class="bi bi-bell me-1"></i>Job Alerts
              </a>
            </li>
            <li class="nav-item mx-3">
              <a class="nav-link btn" href="/talent-pool"
                >Join the Talent Pool</a
              >
            </li>
            <li class="nav-item mx-3">
              <a class="nav-link btn" href="/employers">Employers</a>
            </li>
            <li class="nav-item mx-3">
              <button class="dark-theme-toggle" id="theme-toggle" type="button">
                <i class="bi bi-moon-fill" id="theme-icon"></i>
                <span id="theme-text">Dark</span>
              </button>
            </li>
            <li class="nav-item mx-3">
              <a
                class="nav-link btn-outline-primary btn"
                href="/employer-signup"
                >Post Jobs</a
              >
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <div class="container-fluid">
      <br />
      {% with messages = get_flashed_messages(with_categories=true) %} {% if
      messages %} {% for category, message in messages %}
      <div
        class="alert alert-{{ category }} alert-dismissible fade show"
        role="alert"
      >
        {{ message }}
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="alert"
          aria-label="Close"
        ></button>
      </div>
      {% endfor %} {% endif %} {% endwith %} {% block content %}{% endblock %}

      <br />
    </div>

    <footer class="py-3 my-4 space-from-top">
      <ul class="nav justify-content-center border-bottom pb-3 mb-3">
        <li class="nav-item">
          <a href="/" class="nav-link px-2 text-body-secondary text-primary"
            >Home</a
          >
        </li>
        <li class="nav-item">
          <a
            href="/employers"
            class="nav-link px-2 text-body-secondary text-primary"
            >Employers</a
          >
        </li>
        <li class="nav-item">
          <a href="/jobs" class="nav-link px-2 text-body-secondary text-primary"
            >Jobs</a
          >
        </li>
        <li class="nav-item">
          <a
            href="/about"
            class="nav-link px-2 text-body-secondary text-primary"
            >About Us</a
          >
        </li>
      </ul>
      <p class="text-center text-body-secondary">© 2025 Canvider</p>
    </footer>
  </body>
</html>
