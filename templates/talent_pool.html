{% extends "base.html" %} {% block title %}Join <PERSON> Pool{% endblock %}

<script>
  function validateFileSize(input) {
    const file = input.files[0];
    if (file) {
      const fileSizeInMB = file.size / 1024 / 1024;
      if (fileSizeInMB > 3) {
        alert("File size must be less than 3MB");
        input.value = ""; // Clear the input field
      } else if (file.type !== "application/pdf") {
        alert("File must be a PDF");
        input.value = ""; // Clear the input field
      }
    }
  }
</script>

{% block content %}
<div class="container py-5">
  <!-- Modern Header Section -->
  <div class="text-center mb-5">
    <h1 class="display-4 fw-bold mb-3">
      Join Our Talent Pool
    </h1>
    <p class="lead text-muted mb-4">
      Connect with top employers and unlock exclusive career opportunities
    </p>
    <div class="d-flex justify-content-center flex-wrap gap-3">
      <span class="badge bg-light-1 bg-opacity-10 text-dark px-3 py-2">
        <i class="bi bi-shield-check me-1"></i>Verified Employers Only
      </span>
      <span class="badge bg-light-2 bg-opacity-10 text-dark px-3 py-2">
        <i class="bi bi-lightning me-1"></i>Direct Contact
      </span>
      <span class="badge bg-light-1 bg-opacity-10 text-dark px-3 py-2">
        <i class="bi bi-star me-1"></i>Curated Opportunities
      </span>
    </div>
  </div>

  <div class="row justify-content-center align-items-center">
    <div class="col-12 col-md-8 col-lg-6 d-none d-lg-block">
      <div class="text-center p-4 mb-4">
        <img
          src="./static/MessyDoodle.svg"
          alt="Join talent pool illustration"
          class="img-fluid rounded-3"
          style="max-height: 350px"
        />
      </div>
    </div>

    <div class="col-12 col-md-8 col-lg-6">
      <div class="modern-card rounded-3 shadow-sm p-5 mb-4">
        <div class="text-center mb-4">
          <h3 class="fw-bold text-primary mb-2">Create Your Profile</h3>
          <p class="text-muted">
            Register once and get contacted by employers directly when matching opportunities arise
          </p>
        </div>

        <form action="/talent-pool" method="post" enctype="multipart/form-data">
          <!-- Name and Surname Input -->
          <div class="row mb-4">
            <div class="col-6">
              <div class="form-floating">
                <input
                  type="text"
                  class="form-control border-2"
                  id="firstName"
                  name="firstName"
                  placeholder="John"
                  required
                />
                <label for="firstName">
                  <i class="bi bi-person me-2"></i>First Name
                </label>
              </div>
            </div>
            <div class="col-6">
              <div class="form-floating">
                <input
                  type="text"
                  class="form-control border-2"
                  id="lastName"
                  name="lastName"
                  placeholder="Doe"
                  required
                />
                <label for="lastName">
                  <i class="bi bi-person me-2"></i>Last Name
                </label>
              </div>
            </div>
          </div>

          <!-- Phone Input -->
          <div class="mb-4">
            <div class="form-floating">
              <input
                type="tel"
                class="form-control border-2"
                id="phone"
                name="phone"
                placeholder="+****************"
              />
              <label for="phone">
                <i class="bi bi-telephone me-2"></i>Phone (Optional)
              </label>
            </div>
          </div>
          <!-- Location Input -->
          <div class="mb-4">
            <div class="form-floating">
              <select
                class="form-select border-2"
                id="location"
                name="location"
                required
              >
                <option value="" disabled selected>Select your country</option>
              </select>
              <label for="location">
                <i class="bi bi-geo-alt me-2"></i>Country of Residence
              </label>
            </div>
          </div>

          <script>
          document.addEventListener("DOMContentLoaded", function () {
            const countryDropdown = document.getElementById("location");
            fetch("https://restcountries.com/v3.1/all")
            .then((response) => response.json())
            .then((countries) => {
              const sortedCountries = countries.sort((a, b) =>
              a.name.common.localeCompare(b.name.common)
              );
              sortedCountries.forEach((country) => {
              const option = document.createElement("option");
              option.value = country.name.common;
              option.textContent = country.name.common;
              countryDropdown.appendChild(option);
              });
            })
            .catch((error) => {
              console.error("Error fetching countries:", error);
            });
          });
          </script>

          <!-- Email Input -->
          <div class="mb-4">
            <div class="form-floating">
              <input
                type="email"
                class="form-control border-2"
                id="email"
                name="email"
                placeholder="<EMAIL>"
                required
              />
              <label for="email">
                <i class="bi bi-envelope me-2"></i>Email Address
              </label>
            </div>
          </div>

          <!-- CV Upload -->
          <div class="mb-4">
            <label for="cvUpload" class="form-label fw-semibold mb-3">
              <i class="bi bi-file-earmark-pdf text-primary me-2"></i>Upload Your CV
            </label>
            <input
              type="file"
              class="form-control border-2 p-3"
              id="cvUpload"
              name="cvUpload"
              accept=".pdf"
              onchange="validateFileSize(this)"
              required
            />
            <div class="form-text mt-2">
              <i class="bi bi-info-circle me-1"></i>PDF format only (max 3MB)
            </div>
          </div>

          <!-- Submit Button -->
          <div class="d-grid gap-2 mt-5">
            <button type="submit" class="btn btn-primary btn-lg py-3 shadow-sm">
              <i class="bi bi-person-plus me-2"></i>Join Talent Pool
            </button>
          </div>
        </form>

        <div class="text-center mt-4">
          <small class="text-muted">
            By registering, you agree to be contacted by verified employers
          </small>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
