{% extends "base.html" %} {% block title %} Apply {% endblock %} 
{% block content %}
<div class="less-limited-width-content">
  <div id="listing-div-{{vacancy.vacancy_id}}" class="rounded shadow-sm">
    <div
      class="rounded-top w-100"
      style="background-image: url('{{vacancy.employer_banner_url}}'); height: 200px; background-size: cover;"
    ></div>

    <div class="row g-3 m-2 m-md-4">
      <!-- Main Content -->
      <div class="col-12 col-lg-8 border-lg-end mb-4 mb-lg-0">
        <div class="row align-items-center mb-4">
          <div class="col-4 col-sm-3 col-md-2 p-2">
            <img
              src="{{vacancy.employer_logo_url}}"
              class="img-fluid rounded employer-icon-md"
              alt="Job Icon"
              style="max-width: 80px"
            />
          </div>
          <div class="col-8 col-sm-9 col-md-10">
            <h1 class="h3 h2-lg mb-2">{{vacancy.vacancy_title}}</h1>
            <p class="mb-0 text-muted">{{vacancy.employe_rname}}</p>
          </div>
        </div>

        <hr class="my-3 d-none d-md-block" />

        <div name="job-desc" class="p-2">
          {{ vacancy.vacancy_job_description|safe }}
        </div>
      </div>

      <!-- Sidebar -->
      <div class="col-12 col-lg-4 ps-lg-4">
        <div name="quick-facts" class="mb-4">
          <h2 class="h5 mb-3">Position Facts</h2>
          <div class="mt-3">
            <p class="mb-3">
              <strong>Monthly Salary Budget:</strong><br />
              {{vacancy.salary_min}} - {{vacancy.salary_max}}
              {{vacancy.salary_currency}}
            </p>
            <p class="mb-3">
              <strong>Work Mode:</strong><br />
              {{vacancy.office_schedule}}
            </p>
            <p class="mb-3">
              <strong>Work Location:</strong><br />
              {% if vacancy.vacancy_city %} {{vacancy.vacancy_city}}, {% endif %} {{vacancy.vacancy_country}}
            </p>
          </div>
        </div>

        <hr class="my-3" />

        <div name="highlight-badges" class="my-4">
          <h2 class="h5 mb-3">Key Advantages</h2>
          <div class="d-flex flex-wrap gap-2">
            {% for tag in all_tags[vacancy.vacancy_id] %}
            <span class="badge bg-dark text-wrap p-2">
              <small>{{tag}}</small>
            </span>
            {% endfor %}
          </div>
        </div>

        <hr class="my-3" />

        <div name="apply-now" class="my-4">
          <div class="d-grid gap-2">
            <a
              href="{{url_for('employer',employer_id=vacancy.employer_id)}}"
              target="_blank"
              class="btn btn-outline-dark btn-lg shadow-sm"
            >
              Company Profile <i class="bi bi-arrow-up-right-square-fill"></i>
            </a>
            <button
              type="button"
              class="btn btn-success btn-lg shadow-sm"
              data-bs-toggle="modal"
              data-bs-target="#applicationModal"
            >
              Apply Now
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Application Modal -->
<div class="modal fade" id="applicationModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title h5">Quick Application</h2>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
        ></button>
      </div>
      <div class="modal-body">
        <form action="/submit" enctype="multipart/form-data" method="post">
          <div class="mb-3">
            <label class="form-label">Position ID</label>
            <input
              name="vac_id"
              value="{{vacancy.vacancy_id}}"
              class="form-control"
              readonly
              required
            />
          </div>

          <div class="mb-3">
            <label class="form-label">Position Title</label>
            <input
              value="{{vacancy.vacancy_title}}"
              class="form-control"
              name="vac_title"
              disabled
              required
            />
          </div>

          <input
              value="{{source}}"
              name="source"
              type="hidden"
              required
            />

          <div class="mb-3 row">
            <div class="col-6">
              <label class="form-label">First Name</label>
              <input
                name="fname"
                type="text"
                class="form-control"
                placeholder="John"
                required
              />
            </div>
            <div class="col-6">
              <label class="form-label">Last Name</label>
              <input
                name="lname"
                type="text"
                class="form-control"
                placeholder="Doe"
                required
              />
            </div>
          </div>

          <div class="mb-3">
            <label class="form-label">Email</label>
            <input
              name="email"
              type="email"
              class="form-control"
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div class="mb-3">
            <label class="form-label">Phone</label>
            <input
              name="phone"
              type="tel"
              class="form-control"
              placeholder="+1234567890"
              pattern="^\+?[1-9]\d{1,14}$"
              required
            />
          </div>

          <div class="mb-3">
            <label class="form-label">Notice Period</label>
            <select name="notice" class="form-select" required>
              <option>Ready immediately</option>
              <option>2 weeks</option>
              <option>1 month</option>
              <option>2 months</option>
              <option>3 months</option>
              <option>More than 3 months</option>
            </select>
          </div>

          <div class="mb-4">
            <label class="form-label">Upload CV (PDF only)</label>
            <input
              name="cv"
              type="file"
              class="form-control"
              accept=".pdf"
              onchange="validateFileSize(this)"
              required
            />
          </div>

          <div class="d-grid">
            <button type="submit" class="btn btn-primary btn-lg">
              Submit Application
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script>
  function validateFileSize(input) {
    const file = input.files[0];
    if (file) {
      const fileSizeInMB = file.size / 1024 / 1024;
      if (fileSizeInMB > 3) {
        alert("File size must be less than 3MB");
        input.value = "";
      } else if (file.type !== "application/pdf") {
        alert("File must be a PDF");
        input.value = "";
      }
    }
  }

  // Add a small loading animation to the submit button
  document.addEventListener("DOMContentLoaded", function () {
    const submitButton = document.querySelector(".btn-primary");
    submitButton.addEventListener("click", function () {
      submitButton.innerHTML = "Submitting...";
    });
  });

</script>

<style>
  @media (max-width: 768px) {
    .employer-icon-md {
      max-width: 60px;
    }

    h1 {
      font-size: 1.5rem;
    }

    .modal-content {
      margin: 1rem;
    }

    .btn-lg {
      padding: 0.75rem 1rem;
      font-size: 1rem;
    }
  }
</style>

{% endblock %}
