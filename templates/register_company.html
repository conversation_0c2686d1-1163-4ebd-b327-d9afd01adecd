{% extends "base.html" %} {% block title %}Post Jobs on Our Portal{% endblock %}
{% block content %}
<div class="container py-5">
  <!-- Modern Header Section -->
  <div class="text-center mb-5">
    <h1 class="display-4 fw-bold mb-3">
      <i class="bi bi-building text-primary me-3"></i>
      Partner With Us
    </h1>
    <p class="lead text-muted mb-4">
      Register your company to post verified job opportunities and connect with top talent
    </p>
    <div class="d-flex justify-content-center flex-wrap gap-3">
      <span class="badge bg-light-1 bg-opacity-10 text-dark px-3 py-2">
        <i class="bi bi-check-circle me-1"></i>Free Job Posting
      </span>
      <span class="badge bg-light-2 bg-opacity-10 text-dark px-3 py-2">
        <i class="bi bi-shield-check me-1"></i>Verified Listings
      </span>
      <span class="badge bg-light-1 bg-opacity-10 text-dark px-3 py-2">
        <i class="bi bi-people me-1"></i>Quality Candidates
      </span>
    </div>
  </div>

  <div class="row justify-content-center align-items-center">
    <div class="col-12 col-md-8 col-lg-6 d-none d-lg-block">
      <div class="text-center p-4 mb-4">
        <img
          src="./static/business-card.svg"
          alt="Company registration illustration"
          class="img-fluid rounded-3"
          style="max-height: 350px"
        />
      </div>
    </div>

    <div class="col-12 col-md-8 col-lg-6">
      <div class="modern-card rounded-3 shadow-sm p-5 mb-4">
        <div class="text-center mb-4">
          <h3 class="fw-bold text-primary mb-2">Company Registration</h3>
          <p class="text-muted">
            All postings are free but subject to approval for quality assurance
          </p>
        </div>

        <form
          action="/employer-signup"
          method="post"
          enctype="multipart/form-data"
        >
          <!-- Company Name -->
          <div class="mb-4">
            <div class="form-floating">
              <input
                type="text"
                class="form-control border-2"
                id="companyName"
                name="companyName"
                placeholder="Acme Corp"
                required
              />
              <label for="companyName">
                <i class="bi bi-building me-2"></i>Company Name
              </label>
            </div>
          </div>

          <!-- Contact Person -->
          <div class="mb-4">
            <div class="form-floating">
              <input
                type="text"
                class="form-control border-2"
                id="contactName"
                name="contactName"
                placeholder="Jane Smith"
                required
              />
              <label for="contactName">
                <i class="bi bi-person me-2"></i>Contact Person
              </label>
            </div>
          </div>

          <!-- Email -->
          <div class="mb-4">
            <div class="form-floating">
              <input
                type="email"
                class="form-control border-2"
                id="email"
                name="email"
                placeholder="<EMAIL>"
                required
              />
              <label for="email">
                <i class="bi bi-envelope me-2"></i>Work Email
              </label>
            </div>
          </div>

          <!-- Phone -->
          <div class="mb-4">
            <div class="form-floating">
              <input
                type="tel"
                class="form-control border-2"
                id="phone"
                name="phone"
                placeholder="+****************"
                required
              />
              <label for="phone">
                <i class="bi bi-telephone me-2"></i>Contact Number
              </label>
            </div>
          </div>

          <!-- Company Website -->
          <div class="mb-4">
            <div class="form-floating">
              <input
                type="url"
                class="form-control border-2"
                id="website"
                name="website"
                placeholder="https://www.yourcompany.com"
                pattern="https?://.+"
                title="Please enter a valid URL starting with http:// or https://"
                onblur="if (this.value && !this.value.startsWith('http')) this.value = 'https://' + this.value"
                required
              />
              <label for="website">
                <i class="bi bi-globe me-2"></i>Company Website
              </label>
            </div>
          </div>

          <!-- Verification Questions -->
          <div class="mb-4">
            <label class="form-label fw-semibold mb-3">
              <i class="bi bi-shield-check text-primary me-2"></i>Verification Information
            </label>
            <textarea
              class="form-control border-2 p-3"
              rows="4"
              placeholder="Please provide any information that will help us verify your company (e.g., LinkedIn profile, company registration number, etc.)"
              name="verificationInfo"
            ></textarea>
            <div class="form-text mt-2">
              <i class="bi bi-info-circle me-1"></i>This helps us verify your company faster
            </div>
          </div>

          <!-- Agreement Checkbox -->
          <div class="mb-4 p-3 bg-light rounded-3 border">
            <div class="form-check">
              <input
                type="checkbox"
                class="form-check-input"
                id="agreement"
                name="agreement"
                required
                onchange="toggleSubmitButton()"
              />
              <label class="form-check-label fw-semibold" for="agreement">
                <i class="bi bi-check-circle text-success me-2"></i>
                I agree to post only genuine job opportunities and provide accurate information
              </label>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="d-grid gap-2 mt-5">
            <button
              type="submit"
              class="btn btn-primary btn-lg py-3 shadow-sm"
              id="submitBtn"
              disabled
            >
              <i class="bi bi-send me-2"></i>Submit Registration
            </button>
          </div>
        </form>

        <div class="text-center mt-4 p-3 bg-light rounded-3">
          <small class="text-muted">
            <i class="bi bi-clock me-1"></i>
            We'll contact you within 2 business days to verify your company
          </small>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  function toggleSubmitButton() {
    const agreementCheckbox = document.getElementById("agreement");
    const submitButton = document.getElementById("submitBtn");
    submitButton.disabled = !agreementCheckbox.checked;
  }
</script>
{% endblock %}
