{% extends "base.html" %} {% block title %}{{ employer.name }} | Jobsvider{%
endblock %} {% block content %}
<div
  class="company-header"
  style="background-image: url('{{ employer.employer_banner_url }}')"
></div>

<main class="limited-width-content">
  <div class="company-info-card">
    <!-- Integrated Company Logo -->
    <div class="company-logo-card">
      <img
        src="{{ employer.employer_logo_url }}"
        alt="{{ employer.employer_name }} logo"
        class="w-100 h-100 object-fit-contain"
      />
    </div>

    <div class="text-center mb-5">
      <h1 class="display-6 fw-bold">{{ employer.employer_name }}</h1>
      <div class="d-flex justify-content-center gap-2 mt-3">
        <span class="badge bg-light text-dark rounded-pill px-3 py-2">
          {{ employer.employer_industry }}
        </span>
        <span class="badge bg-light text-dark rounded-pill px-3 py-2">
          <i class="bi bi-people-fill me-1"></i>{{ employer.employer_headcount }}+
          Employees
        </span>
      </div>
    </div>

    <div class="detail-section">
      <div class="row g-4">
        <!-- Contact Information Section -->
        <div class="col-md-4">
          <h5 class="contact-section-title">Contact Information</h5>
          {% if employer.employer_phone %}
          <div class="contact-method">
            <i class="bi bi-telephone-outbound-fill"></i>
            <div>
              <small class="text-muted">Phone</small>
              <div class="fw-bold">{{ employer.employer_phone }}</div>
            </div>
          </div>
          {% endif %}
          {% if employer.employer_email %}
          <div class="contact-method">
            <i class="bi bi-envelope-at-fill"></i>
            <div>
              <small class="text-muted">Email</small>
              <div class="fw-bold">{{ employer.employer_email }}</div>
            </div>
          </div>
          {% endif %}
        </div>

        <!-- Social Media Section -->
        <div class="col-md-4">
          <h5 class="contact-section-title">Social Media</h5>
          {% for portal in social_portals %}
          <a href="{{ portal.url }}" class="social-link" target="_blank">
            <i class="bi bi-{{ portal.location }}"></i>
            <span>{{ portal.location|title }}</span>
          </a>
          {% endfor %}
        </div>

        <!-- Office Locations Section -->
        <div class="col-md-4">
          <h5 class="contact-section-title">Office Locations</h5>
          {% for location in locations %}
          <div class="d-flex align-items-center gap-2 mb-2">
            <i class="bi bi-geo-alt-fill text-muted"></i>
            <span>{{ location.location }}</span>
          </div>
          {% endfor %}
        </div>
      </div>
    </div>

    <!-- About Us Section -->
    <div class="detail-section">
      <h3 class="mb-4">About Us</h3>
      <div class="lead" style="line-height: 1.6">
        {{ employer.employer_description }}
      </div>
    </div>
    <!-- Gallery Section -->
    {% if all_gallery %}
    <div class="detail-section">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h3 class="mb-0">Our Photos</h3>
        <button
          class="btn btn-outline-primary"
          data-bs-toggle="modal"
          data-bs-target="#gallery-modal"
        >
          View All {{ all_gallery|length }} Photos
        </button>
      </div>
      <div class="gallery-grid">
        {% for photo in all_gallery[:4] %}
        <img
          src="https://canvider-public.fra1.cdn.digitaloceanspaces.com/{{ photo }}"
          class="gallery-item"
          alt="Company photo"
          data-bs-toggle="modal"
          data-bs-target="#gallery-modal"
        />
        {% endfor %}
      </div>
    </div>
    {% endif %}

    <!-- Job Listings Section -->
    <div class="detail-section">
      <h3 class="mb-4">Open Positions</h3>
      {% if not vacancies %}
      <div class="lead">
        No open positions available at the moment.
      </div>
      {% else %}
      <div class="job-listings">
        {% for job in vacancies %}
        <a
          href="/apply/{{ job.vacancy_id }}"
          class="text-decoration-none text-dark"
        >
          <div class="job-listing">
            <div class="d-flex align-items-center gap-3">
              <img
                src="{{ job.employer_logo_url }}"
                alt="{{ job.employer_name }} logo"
                class="company-logo"
                style="width: 60px; height: 60px"
              />
              <div class="flex-grow-1">
                <h5 class="mb-1">{{ job.vacancy_title }}</h5>
                <div class="d-flex align-items-center gap-2 text-muted mb-2">
                  <span>{{ job.employer_name }}</span>
                </div>
                <div class="d-flex gap-2 flex-wrap">
                  <span class="badge bg-light text-dark rounded-pill py-2 px-3">
                    <i class="bi bi-globe me-1"></i>{{ job.office_schedule }}
                  </span>
                  <span class="badge bg-light text-dark rounded-pill py-2 px-3">
                    <i class="bi bi-geo-alt me-1"></i>{{ job.vacancy_country }}
                  </span>
                </div>
              </div>
              <div class="text-end">
                <div class="salary-highlight fw-bold">
                  {{ job.salary_currency }} {{ job.salary_min }} - {{
                  job.salary_max }}
                </div>
                <small class="text-muted">per month</small>
              </div>
            </div>
          </div>
        </a>
        {% endfor %}
      </div>
    </div>
    {% endif %}
  </div>
</main>

<!-- Gallery Modal -->
{% if all_gallery %}
<div class="modal fade" id="gallery-modal">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <!-- Modal Header -->
      <div class="modal-header">
        <h4 class="modal-title">Gallery</h4>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
        ></button>
      </div>

      <!-- Modal body -->
      <div class="modal-body">
        <div
          id="carouselExampleControls"
          class="carousel carousel-dark slide"
          data-bs-ride="carousel"
        >
            <div class="carousel-inner">
            <div class="carousel-item active">
              <img
              src="https://canvider-public.fra1.cdn.digitaloceanspaces.com/{{ all_gallery[0] }}"
              class="img-fluid rounded mx-auto d-block"
              style="max-height: 60vh; width: auto;"
              />
            </div>

            {% for item in all_gallery[1:] %}
            <div class="carousel-item">
              <img
              src="https://canvider-public.fra1.cdn.digitaloceanspaces.com/{{item}}"
              class="img-fluid rounded mx-auto d-block"
              style="max-height: 60vh; width: auto;"
              />
            </div>
            {% endfor %}
            </div>

          <button
            class="carousel-control-prev"
            type="button"
            data-bs-target="#carouselExampleControls"
            data-bs-slide="prev"
          >
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Previous</span>
          </button>
          <button
            class="carousel-control-next"
            type="button"
            data-bs-target="#carouselExampleControls"
            data-bs-slide="next"
          >
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Next</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
{% endif %}

{% endblock %}
