{% extends "base.html" %} {% block title %}{{ employer.name }} | Jobsvider{%
endblock %} {% block content %}

<!-- Modern Company Header with Gradient Overlay -->
<div class="company-header-modern" style="background-image: url('{{ employer.employer_banner_url }}')">
  <div class="company-header-overlay"></div>
  <div class="company-header-content">
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-lg-8 text-center">
          <div class="company-logo-modern">
            <img
              src="{{ employer.employer_logo_url }}"
              alt="{{ employer.employer_name }} logo"
              class="company-logo-img"
              onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
            />
            <div class="company-logo-fallback" style="display: none;">
              {{ (employer.employer_name or 'C')[0]|upper }}
            </div>
          </div>
          <h1 class="company-title">{{ employer.employer_name }}</h1>
          <p class="company-subtitle">{{ employer.employer_industry }}</p>
          <div class="company-badges">
            <span class="company-badge">
              <i class="bi bi-people-fill me-2"></i>{{ employer.employer_headcount }}+ Employees
            </span>
            {% if employer.employer_country %}
            <span class="company-badge">
              <i class="bi bi-geo-alt-fill me-2"></i>{{ employer.employer_country }}
            </span>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<main class="limited-width-content">
  <div class="company-content-wrapper">

    <!-- Modern Contact & Info Section -->
    <div class="company-info-section">
      <div class="row g-4">
        <!-- Contact Information Card -->
        <div class="col-lg-4 col-md-6">
          <div class="info-card contact-card">
            <div class="info-card-header">
              <i class="bi bi-telephone-fill"></i>
              <h5>Contact Information</h5>
            </div>
            <div class="info-card-body">
              {% if employer.employer_phone %}
              <div class="contact-item">
                <div class="contact-icon">
                  <i class="bi bi-telephone-outbound-fill"></i>
                </div>
                <div class="contact-details">
                  <span class="contact-label">Phone</span>
                  <span class="contact-value">{{ employer.employer_phone }}</span>
                </div>
              </div>
              {% endif %}
              {% if employer.employer_email %}
              <div class="contact-item">
                <div class="contact-icon">
                  <i class="bi bi-envelope-at-fill"></i>
                </div>
                <div class="contact-details">
                  <span class="contact-label">Email</span>
                  <span class="contact-value">{{ employer.employer_email }}</span>
                </div>
              </div>
              {% endif %}
              {% if not employer.employer_phone and not employer.employer_email %}
              <div class="no-contact-info">
                <i class="bi bi-info-circle text-muted"></i>
                <span class="text-muted">Contact information not available</span>
              </div>
              {% endif %}
            </div>
          </div>
        </div>

        <!-- Social Media Card -->
        <div class="col-lg-4 col-md-6">
          <div class="info-card social-card">
            <div class="info-card-header">
              <i class="bi bi-share-fill"></i>
              <h5>Social Media</h5>
            </div>
            <div class="info-card-body">
              {% if social_portals %}
              {% for portal in social_portals %}
              <a href="{{ portal.url }}" class="social-link-modern" target="_blank">
                <div class="social-icon">
                  <i class="bi bi-{{ portal.location }}"></i>
                </div>
                <span class="social-name">{{ portal.location|title }}</span>
                <i class="bi bi-arrow-up-right social-arrow"></i>
              </a>
              {% endfor %}
              {% else %}
              <div class="no-social-info">
                <i class="bi bi-info-circle text-muted"></i>
                <span class="text-muted">No social media links available</span>
              </div>
              {% endif %}
            </div>
          </div>
        </div>

        <!-- Office Locations Card -->
        <div class="col-lg-4 col-md-12">
          <div class="info-card location-card">
            <div class="info-card-header">
              <i class="bi bi-geo-alt-fill"></i>
              <h5>Office Locations</h5>
            </div>
            <div class="info-card-body">
              {% if locations %}
              {% for location in locations %}
              <div class="location-item">
                <div class="location-icon">
                  <i class="bi bi-building"></i>
                </div>
                <span class="location-name">{{ location.location }}</span>
              </div>
              {% endfor %}
              {% else %}
              <div class="no-location-info">
                <i class="bi bi-info-circle text-muted"></i>
                <span class="text-muted">Office locations not specified</span>
              </div>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- About Us Section -->
    <div class="company-about-section">
      <div class="section-header">
        <h3 class="section-title">
          <i class="bi bi-info-circle-fill me-3"></i>About {{ employer.employer_name }}
        </h3>
        <div class="section-divider"></div>
      </div>
      <div class="about-content">
        <div class="about-text">
          {{ employer.employer_description or "No company description available." }}
        </div>
      </div>
    </div>
    <!-- Gallery Section -->
    {% if all_gallery %}
    <div class="company-gallery-section">
      <div class="section-header">
        <div class="d-flex justify-content-between align-items-center">
          <h3 class="section-title">
            <i class="bi bi-images me-3"></i>Company Gallery
          </h3>
          <button
            class="btn btn-outline-primary btn-modern"
            data-bs-toggle="modal"
            data-bs-target="#gallery-modal"
          >
            <i class="bi bi-eye me-2"></i>View All {{ all_gallery|length }} Photos
          </button>
        </div>
        <div class="section-divider"></div>
      </div>
      <div class="gallery-grid-modern">
        {% for photo in all_gallery[:6] %}
        <div class="gallery-item-modern" data-aos="fade-up" data-aos-delay="{{ loop.index0 * 100 }}">
          <img
            src="https://canvider-public.fra1.cdn.digitaloceanspaces.com/{{ photo }}"
            class="gallery-image"
            alt="Company photo {{ loop.index }}"
            data-bs-toggle="modal"
            data-bs-target="#gallery-modal"
            loading="lazy"
          />
          <div class="gallery-overlay">
            <i class="bi bi-zoom-in"></i>
          </div>
        </div>
        {% endfor %}
      </div>
    </div>
    {% endif %}

    <!-- Job Listings Section -->
    <div class="company-jobs-section">
      <div class="section-header">
        <h3 class="section-title">
          <i class="bi bi-briefcase-fill me-3"></i>Open Positions
          {% if vacancies %}
          <span class="job-count-badge">{{ vacancies|length }}</span>
          {% endif %}
        </h3>
        <div class="section-divider"></div>
      </div>

      {% if not vacancies %}
      <div class="no-jobs-message">
        <div class="no-jobs-icon">
          <i class="bi bi-briefcase"></i>
        </div>
        <h4>No Open Positions</h4>
        <p class="text-muted">This company doesn't have any open positions at the moment. Check back later for new opportunities!</p>
        <a href="/jobs" class="btn btn-primary">
          <i class="bi bi-search me-2"></i>Explore Other Jobs
        </a>
      </div>
      {% else %}
      <div class="jobs-grid-modern">
        {% for job in vacancies %}
        <div class="job-card-company" data-aos="fade-up" data-aos-delay="{{ loop.index0 * 100 }}">
          <a href="/apply/{{ job.vacancy_id }}" class="job-card-link">
            <div class="job-card-header">
              <div class="job-company-logo">
                <img
                  src="{{ job.employer_logo_url }}"
                  alt="{{ job.employer_name }} logo"
                  class="company-logo-img"
                  onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                />
                <div class="company-logo-fallback" style="display: none;">
                  {{ (job.employer_name or 'C')[0]|upper }}
                </div>
              </div>
              <div class="job-meta">
                <span class="job-posted">{{ job.vacancy_age or 'Recently posted' }}</span>
              </div>
            </div>

            <div class="job-card-body">
              <h5 class="job-title">{{ job.vacancy_title }}</h5>
              <p class="job-company">{{ job.employer_name }}</p>

              <div class="job-tags-modern">
                {% if job.office_schedule %}
                <span class="job-tag job-tag-primary">
                  <i class="bi bi-{{ 'house' if 'Remote' in (job.office_schedule or '') else 'building' }}"></i>
                  {{ job.office_schedule }}
                </span>
                {% endif %}
                {% if job.vacancy_country %}
                <span class="job-tag job-tag-secondary">
                  <i class="bi bi-geo-alt"></i>
                  {{ job.vacancy_country }}
                </span>
                {% endif %}
                {% if job.vacancy_type %}
                <span class="job-tag job-tag-accent">
                  <i class="bi bi-clock"></i>
                  {{ job.vacancy_type }}
                </span>
                {% endif %}
              </div>
            </div>

            <div class="job-card-footer">
              {% if job.salary_min and job.salary_max %}
              <div class="salary-info">
                <div class="salary-amount">
                  {{ job.salary_currency }} {{ job.salary_min }} - {{ job.salary_max }}
                </div>
                <div class="salary-period">per month</div>
              </div>
              {% else %}
              <div class="salary-competitive">
                <span>Competitive Salary</span>
              </div>
              {% endif %}
              <div class="apply-arrow">
                <i class="bi bi-arrow-right"></i>
              </div>
            </div>
          </a>
        </div>
        {% endfor %}
      </div>
      {% endif %}
    </div>
  </div>
</main>

<!-- Gallery Modal -->
{% if all_gallery %}
<div class="modal fade" id="gallery-modal">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <!-- Modal Header -->
      <div class="modal-header">
        <h4 class="modal-title">Gallery</h4>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
        ></button>
      </div>

      <!-- Modal body -->
      <div class="modal-body">
        <div
          id="carouselExampleControls"
          class="carousel carousel-dark slide"
          data-bs-ride="carousel"
        >
            <div class="carousel-inner">
            <div class="carousel-item active">
              <img
              src="https://canvider-public.fra1.cdn.digitaloceanspaces.com/{{ all_gallery[0] }}"
              class="img-fluid rounded mx-auto d-block"
              style="max-height: 60vh; width: auto;"
              />
            </div>

            {% for item in all_gallery[1:] %}
            <div class="carousel-item">
              <img
              src="https://canvider-public.fra1.cdn.digitaloceanspaces.com/{{item}}"
              class="img-fluid rounded mx-auto d-block"
              style="max-height: 60vh; width: auto;"
              />
            </div>
            {% endfor %}
            </div>

          <button
            class="carousel-control-prev"
            type="button"
            data-bs-target="#carouselExampleControls"
            data-bs-slide="prev"
          >
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Previous</span>
          </button>
          <button
            class="carousel-control-next"
            type="button"
            data-bs-target="#carouselExampleControls"
            data-bs-slide="next"
          >
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Next</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
{% endif %}

{% endblock %}
