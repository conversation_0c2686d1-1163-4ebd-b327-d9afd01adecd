"""
Background Scheduler System
Handles daily RSS feed updates and job alert notifications.
"""

import logging
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.executors.pool import ThreadPoolExecutor
from datetime import datetime
import atexit

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class JobScheduler:
    """
    Manages background scheduled tasks for the application.
    """
    
    def __init__(self, app=None):
        self.app = app
        self.scheduler = None
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize the scheduler with Flask app."""
        self.app = app
        
        # Configure scheduler
        executors = {
            'default': ThreadPoolExecutor(max_workers=2)
        }
        
        job_defaults = {
            'coalesce': False,
            'max_instances': 1,
            'misfire_grace_time': 300  # 5 minutes
        }
        
        self.scheduler = BackgroundScheduler(
            executors=executors,
            job_defaults=job_defaults,
            timezone='UTC'
        )
        
        # Register shutdown handler
        atexit.register(self.shutdown)
    
    def start(self):
        """Start the scheduler."""
        if self.scheduler and not self.scheduler.running:
            try:
                self.scheduler.start()
                logger.info("Background scheduler started successfully")
                
                # Schedule RSS feed updates twice daily
                self.scheduler.add_job(
                    func=self.update_rss_feeds,
                    trigger=CronTrigger(hour='10,22'),  # Run at 10 AM and 10 PM UTC
                    id='daily_rss_update',
                    name='Twice Daily RSS Feed Update',
                    replace_existing=True
                )
                
                # Schedule job alert processing every 4 hours
                self.scheduler.add_job(
                    func=self.process_job_alerts,
                    trigger=CronTrigger(hour='*/4'),  # Run every 4 hours
                    id='job_alerts',
                    name='Job Alert Processing',
                    replace_existing=True
                )
                
                # Schedule weekend catch-up job for missed alerts
                self.scheduler.add_job(
                    func=self.process_job_alerts,
                    trigger=CronTrigger(day_of_week='sat', hour=12),  # Run Saturday at noon UTC
                    id='weekend_catchup',
                    name='Weekend Alert Catch-up',
                    replace_existing=True
                )
                
                logger.info("Scheduled jobs registered successfully")
                
            except Exception as e:
                logger.error(f"Error starting scheduler: {str(e)}")
    
    def shutdown(self):
        """Shutdown the scheduler."""
        if self.scheduler and self.scheduler.running:
            try:
                self.scheduler.shutdown(wait=False)
                logger.info("Background scheduler shut down successfully")
            except Exception as e:
                logger.error(f"Error shutting down scheduler: {str(e)}")
    
    def update_rss_feeds(self):
        """
        Daily RSS feed update job.
        This function is called by the scheduler at 5:00 AM daily.
        """
        logger.info("Starting scheduled RSS feed update")
        
        try:
            with self.app.app_context():
                from custom_libs.rss_manager import rss_manager
                
                # Update all RSS feeds
                stats = rss_manager.update_all_feeds()
                
                logger.info(f"RSS feed update completed: {stats}")
                
                # Log statistics
                if stats.get('errors', 0) > 0:
                    logger.warning(f"RSS update completed with {stats['errors']} errors")
                else:
                    logger.info(f"RSS update successful: cached {stats.get('total_cached', 0)} jobs")
                
        except Exception as e:
            logger.error(f"Error in scheduled RSS feed update: {str(e)}")
    
    def process_job_alerts(self):
        """
        Daily job alert processing job.
        This function is called by the scheduler at 6:00 AM daily.
        """
        logger.info("Starting scheduled job alert processing")
        
        try:
            with self.app.app_context():
                from custom_libs.notification_system import notification_system
                
                # Process all job alerts
                stats = notification_system.process_all_alerts()
                
                logger.info(f"Job alert processing completed: {stats}")
                
                # Log statistics
                if stats.get('errors', 0) > 0:
                    logger.warning(f"Job alert processing completed with {stats['errors']} errors")
                else:
                    logger.info(f"Job alert processing successful: processed {stats.get('alerts_processed', 0)} alerts, sent {stats.get('emails_sent', 0)} emails")
                
        except Exception as e:
            logger.error(f"Error in scheduled job alert processing: {str(e)}")
    
    def add_manual_job(self, func, trigger, job_id, name, **kwargs):
        """
        Add a manual job to the scheduler.
        
        Args:
            func: Function to execute
            trigger: APScheduler trigger
            job_id: Unique job ID
            name: Human-readable job name
            **kwargs: Additional job arguments
        """
        try:
            self.scheduler.add_job(
                func=func,
                trigger=trigger,
                id=job_id,
                name=name,
                replace_existing=True,
                **kwargs
            )
            logger.info(f"Added manual job: {name} ({job_id})")
        except Exception as e:
            logger.error(f"Error adding manual job {job_id}: {str(e)}")
    
    def remove_job(self, job_id):
        """
        Remove a job from the scheduler.
        
        Args:
            job_id: Job ID to remove
        """
        try:
            self.scheduler.remove_job(job_id)
            logger.info(f"Removed job: {job_id}")
        except Exception as e:
            logger.error(f"Error removing job {job_id}: {str(e)}")
    
    def get_jobs(self):
        """
        Get list of all scheduled jobs.
        
        Returns:
            list: List of job information
        """
        try:
            jobs = []
            for job in self.scheduler.get_jobs():
                jobs.append({
                    'id': job.id,
                    'name': job.name,
                    'next_run_time': job.next_run_time,
                    'trigger': str(job.trigger)
                })
            return jobs
        except Exception as e:
            logger.error(f"Error getting jobs: {str(e)}")
            return []
    
    def run_job_now(self, job_id):
        """
        Run a scheduled job immediately.
        
        Args:
            job_id: Job ID to run
            
        Returns:
            bool: Success status
        """
        try:
            job = self.scheduler.get_job(job_id)
            if job:
                # Run the job function directly
                if job_id == 'daily_rss_update':
                    self.update_rss_feeds()
                elif job_id == 'daily_job_alerts':
                    self.process_job_alerts()
                else:
                    job.func()
                
                logger.info(f"Manually executed job: {job_id}")
                return True
            else:
                logger.warning(f"Job not found: {job_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error running job {job_id}: {str(e)}")
            return False


# Global scheduler instance
job_scheduler = JobScheduler()
