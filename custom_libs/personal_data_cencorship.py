# -*- coding: utf-8 -*-
import re
from typing import List, Set

class UnicodeAwareCVCensor:
    def __init__(self):
        self.regex_patterns = {
            'email': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            # International format with country code (must start with +)
            'phone_international': r'\+\d{1,4}[-.\s]?\d{2,4}[-.\s]?\d{2,4}[-.\s]?\d{2,4}(?:[-.\s]?\d{1,4})?',
            # Polish phone numbers specifically (+48 xxx xxx xxx)
            'phone_polish': r'\+48[-.\s]?\d{2,3}[-.\s]?\d{3}[-.\s]?\d{3,4}',
            # Turkish mobile phone numbers (+90 5xx xxx xxxx)
            'phone_turkish': r'\+90[-.\s]?5\d{2}[-.\s]?\d{3}[-.\s]?\d{4}',
            # US format with area code in parentheses or with dashes/spaces
            'phone_us': r'(?:\+?1[-.\s]?)?\(?\d{3}\)?[-.\s]\d{3}[-.\s]\d{4}',
            # European mobile format (must have country code or be 9+ digits)
            'phone_mobile': r'(?:\+\d{1,4}[-.\s]?)?\d{9,15}(?=\s|$|[^\d])',
            # Generic phone (must have separators like spaces, dashes, or parentheses)
            'phone_with_separators': r'\b\d{2,4}[-.\s]\d{2,4}[-.\s]\d{2,8}\b',
            'website': r'https?://[^\s]+|www\.[^\s]+',
            'linkedin': r'linkedin\.com/in/[^\s]+',
            'github': r'github\.com/[^\s]+',
            'gitlab': r'gitlab\.com/[^\s]+',
        }
        
        self.replacements = {
            'email': '[EMAIL]',
            'phone_international': '[PHONE]',
            'phone_polish': '[PHONE]',
            'phone_turkish': '[PHONE]',
            'phone_us': '[PHONE]',
            'phone_mobile': '[PHONE]',
            'phone_with_separators': '[PHONE]',
            'website': '[WEBSITE]',
            'linkedin': '[LINKEDIN]',
            'github': '[GITHUB]',
            'gitlab': '[GITLAB]',
        }
        
        # Character variations mapping
        self.char_variations = {
            'a': ['a', 'á', 'à', 'â', 'ä', 'ã', 'å', 'ā', 'ă', 'ą'],
            'e': ['e', 'é', 'è', 'ê', 'ë', 'ē', 'ĕ', 'ė', 'ę', 'ě'],
            'i': ['i', 'í', 'ì', 'î', 'ï', 'ĩ', 'ī', 'ĭ', 'į'],
            'o': ['o', 'ó', 'ò', 'ô', 'ö', 'õ', 'ō', 'ŏ', 'ő'],
            'u': ['u', 'ú', 'ù', 'û', 'ü', 'ũ', 'ū', 'ŭ', 'ů', 'ű', 'ų'],
            'c': ['c', 'ç', 'ć', 'ĉ', 'ċ', 'č'],
            'n': ['n', 'ñ', 'ń', 'ņ', 'ň'],
            's': ['s', 'ś', 'ŝ', 'ş', 'š'],
            'z': ['z', 'ź', 'ż', 'ž'],
            'l': ['l', 'ł', 'ĺ', 'ļ', 'ľ'],
            'r': ['r', 'ŕ', 'ŗ', 'ř'],
            'g': ['g', 'ğ', 'ĝ', 'ġ', 'ģ'],
        }
    
    def generate_diacritic_variations(self, text: str) -> Set[str]:
        variations = set()
        text_lower = text.lower()
        
        def generate_recursive(current_text: str, position: int, current_variation: str):
            if position >= len(current_text):
                variations.add(current_variation)
                return
            
            current_char = current_text[position]
            if current_char in self.char_variations:
                for variant in self.char_variations[current_char]:
                    # Only use lowercase variants
                    if variant.islower():
                        generate_recursive(current_text, position + 1, current_variation + variant)
            else:
                generate_recursive(current_text, position + 1, current_variation + current_char)
        
        if len(text_lower) <= 8:
            generate_recursive(text_lower, 0, "")
        else:
            variations.add(text_lower)
        
        return variations

    def create_name_variations(self, first_name: str, last_name: str) -> List[str]:
        first_diacritics = self.generate_diacritic_variations(first_name)
        last_diacritics = self.generate_diacritic_variations(last_name)
        
        all_variations = set()
        
        for base in first_diacritics:
            all_variations.add(base.upper())          # ALL CAPS
            all_variations.add(base.capitalize())     # First letter capital
            all_variations.add(base)                  # all lowercase
        
        for base in last_diacritics:
            all_variations.add(base.upper())
            all_variations.add(base.capitalize())
            all_variations.add(base)
        
        first_sample = sorted(list(first_diacritics))[:20]
        last_sample = sorted(list(last_diacritics))[:20]
        
        for f in first_sample:
            for l in last_sample:
                for style in ['upper', 'capitalize', 'lower']:
                    if style == 'upper':
                        f_style = f.upper()
                        l_style = l.upper()
                    elif style == 'capitalize':
                        f_style = f.capitalize()
                        l_style = l.capitalize()
                    else:  # lower
                        f_style = f
                        l_style = l
                    
                    # Add different separator formats
                    all_variations.add(f"{f_style} {l_style}")
                    all_variations.add(f"{l_style} {f_style}")
                    all_variations.add(f"{f_style}-{l_style}")
        
        # Filter and sort variations
        filtered = [v for v in all_variations if len(v) > 1]
        return sorted(filtered, key=len, reverse=True)
    
    def remove_known_names(self, text: str, first_name: str, last_name: str) -> str:
        print(f"Input: first_name='{first_name}', last_name='{last_name}'")
        
        # Generate all variations
        name_variations = self.create_name_variations(first_name, last_name)
        
        print(f"Generated {len(name_variations)} total variations")
        print("Sample variations:", name_variations[:15])
        
        censored = text
        replacements_made = []
        
        # Replace each variation (longest first to avoid partial replacements)
        for variation in name_variations:
            if len(variation.strip()) > 1:
                # Use word boundaries for better matching
                pattern = r'\b' + re.escape(variation) + r'\b'
                
                try:
                    if re.search(pattern, censored, re.IGNORECASE):
                        censored = re.sub(pattern, '[CANDIDATE_NAME]', censored, flags=re.IGNORECASE)
                        replacements_made.append(f"Replaced: '{variation}'")
                        print(f"✓ Replaced: '{variation}'")
                except Exception as e:
                    print(f"Error with pattern '{variation}': {e}")
                    # Fallback to simple string replacement
                    if variation in censored:
                        censored = censored.replace(variation, '[CANDIDATE_NAME]')
                        replacements_made.append(f"Simple replacement: '{variation}'")
                        print(f"✓ Simple replacement: '{variation}'")
        
        print(f"\nTotal replacements made: {len(set(replacements_made))}")
        
        return censored
    
    def apply_regex_censoring(self, text: str) -> str:
        censored = text
        for category, pattern in self.regex_patterns.items():
            censored = re.sub(pattern, self.replacements[category], censored, flags=re.IGNORECASE)
        return censored
    
    def censor_cv(self, cv_text: str, first_name: str = None, last_name: str = None) -> str:
        try:
            
            if isinstance(cv_text, bytes):
                cv_text = cv_text.decode('utf-8', errors='replace')
            
            censored = cv_text
        except UnicodeDecodeError:
            # If decoding fails, replace invalid characters
            censored = cv_text.decode('utf-8', errors='replace') if isinstance(cv_text, bytes) else cv_text.encode('utf-8', errors='replace').decode('utf-8')
        
        if first_name and last_name:
            
            try:
                first_name = first_name.decode('utf-8') if isinstance(first_name, bytes) else first_name
                last_name = last_name.decode('utf-8') if isinstance(last_name, bytes) else last_name
            except UnicodeDecodeError:
                # If decoding fails, replace invalid characters
                first_name = first_name.decode('utf-8', errors='replace') if isinstance(first_name, bytes) else first_name
                last_name = last_name.decode('utf-8', errors='replace') if isinstance(last_name, bytes) else last_name
            censored = self.remove_known_names(censored, first_name, last_name)
        censored = self.apply_regex_censoring(censored)
        
        return censored

def test_unicode_censor():
    censor = UnicodeAwareCVCensor()

    cv_text = """CV TEXT SHOULD BE HERE"""

    #print("Testing with 'Efe' and 'Ergun':")
    result = censor.censor_cv(cv_text, "Efe", "Ergun")
    print(f"\nResult: {result}")
    
    return censor

if __name__ == "__main__":
    test_unicode_censor()
    

