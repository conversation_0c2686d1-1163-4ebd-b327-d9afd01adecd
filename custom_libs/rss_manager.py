"""
RSS Feed Management System
Handles caching and updating of RSS job feeds for better performance.
"""

import feedparser
import logging
from datetime import datetime
from custom_libs.models import db, RSSFeedCache
from sqlalchemy.exc import SQLAlchemyError

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RSSFeedManager:
    """
    Manages RSS feed collection, caching, and updates.
    """
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize the RSS manager with Flask app."""
        self.app = app
    
    def collect_and_cache_we_work_remotely(self, keyword=""):
        """
        Collect jobs from WeWorkRemotely RSS feed and cache them in database.
        
        Args:
            keyword (str): Optional keyword filter
            
        Returns:
            int: Number of jobs cached
        """
        try:
            # Get converted vacancies using the new logic
            converted_vacancies = self.collect_rss_vacancies_we_work_remotely(keyword)
            
            cached_count = 0
            for vacancy_data in converted_vacancies:
                try:
                    # Add is_active field required by our database
                    vacancy_data['is_active'] = True
                    
                    # Check if already exists
                    existing = RSSFeedCache.query.filter_by(
                        vacancy_id=vacancy_data['vacancy_id']
                    ).first()
                    
                    if existing:
                        # Update existing record
                        for key, value in vacancy_data.items():
                            setattr(existing, key, value)
                        existing.updated_at = datetime.utcnow()
                    else:
                        # Create new record
                        new_vacancy = RSSFeedCache(**vacancy_data)
                        db.session.add(new_vacancy)
                    
                    cached_count += 1
                    
                except Exception as e:
                    logger.error(f"Error processing vacancy: {str(e)}")
                    continue
            
            db.session.commit()
            logger.info(f"Successfully cached {cached_count} jobs from WeWorkRemotely")
            return cached_count
            
        except Exception as e:
            logger.error(f"Error collecting RSS feed: {str(e)}")
            db.session.rollback()
            return 0
    
    def collect_rss_vacancies_we_work_remotely(self, keyword=None):
        """
        Collect and convert vacancies from WeWorkRemotely RSS feed.

        Args:
            keyword (str): Optional keyword filter

        Returns:
            list: List of converted vacancy dictionaries
        """
        try:
            import requests

            feed_url = "https://weworkremotely.com/categories/remote-programming-jobs.rss"
            logger.info(f"Fetching RSS feed from: {feed_url}")

            # Add headers to bypass Cloudflare protection
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/rss+xml, application/xml, text/xml',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }

            # Fetch the RSS feed with proper headers and retry logic
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    response = requests.get(feed_url, headers=headers, timeout=30)
                    response.raise_for_status()
                    break
                except requests.exceptions.RequestException as e:
                    if attempt == max_retries - 1:
                        logger.error(f"Failed to fetch RSS feed after {max_retries} attempts: {str(e)}")
                        return []

                    import time
                    wait_time = (2 ** attempt) + 1  # Exponential backoff: 2, 5, 9 seconds
                    logger.warning(f"Attempt {attempt + 1} failed, retrying in {wait_time} seconds: {str(e)}")
                    time.sleep(wait_time)

            # Parse the RSS feed
            feed = feedparser.parse(response.content)
            logger.info(f"Found {len(feed.entries)} jobs in WeWorkRemotely RSS feed")
            
            # Apply keyword filtering if provided
            if keyword and keyword.strip():
                # Handle both "%keyword%" format and plain keyword
                search_keyword = keyword.strip('%').lower() if keyword.startswith('%') and keyword.endswith('%') else keyword.lower()
                filtered_entries = []
                for item in feed.entries:
                    try:
                        # Split title to get job title (after company name)
                        title_parts = item.title.split(": ", 1)
                        job_title = title_parts[1] if len(title_parts) > 1 else item.title
                        if search_keyword in job_title.lower():
                            filtered_entries.append(item)
                    except (IndexError, AttributeError):
                        # If title parsing fails, include the item
                        filtered_entries.append(item)
                feed.entries = filtered_entries
            
            converted_vacancies = []
            for item in feed.entries:
                try:
                    # Parse title safely
                    title_parts = item.title.split(": ", 1)
                    if len(title_parts) >= 2:
                        company = title_parts[0].strip()
                        title = title_parts[1].strip()
                    else:
                        company = "Unknown Company"
                        title = item.title.strip()

                    # Get description safely
                    description = getattr(item, 'summary', '') or getattr(item, 'description', '')

                    # Get media content safely
                    logo_url = ""
                    if hasattr(item, 'media_content') and item.media_content:
                        try:
                            logo_url = item.media_content[0].get('url', '') if item.media_content else ''
                        except (IndexError, AttributeError):
                            logo_url = ""

                    vacancy = {
                        "title": title,
                        "company": company,
                        "description": description,
                        "link": getattr(item, 'link', ''),
                        "published": getattr(item, 'published', ''),
                        "region": getattr(item, 'region', ''),
                        "type": getattr(item, 'type', ''),
                        "logo_url": logo_url,
                    }
                    
                    # Convert to our vacancy format
                    # Parse job description safely
                    job_description = vacancy["description"]
                    company_url = ""

                    # Try to extract clean job description and company URL
                    try:
                        if "</a>\n</p>\n" in job_description:
                            parts = job_description.split("</a>\n</p>\n", 1)
                            if len(parts) > 1:
                                desc_part = parts[1]
                                if "<strong>To apply:</strong>" in desc_part:
                                    job_description = desc_part.split("<strong>To apply:</strong>")[0].strip()
                                else:
                                    job_description = desc_part.strip()

                        # Extract company URL
                        if "<strong>URL:</strong> <a href=" in vacancy["description"]:
                            url_parts = vacancy["description"].split("<strong>URL:</strong> <a href=", 1)
                            if len(url_parts) > 1:
                                url_part = url_parts[1]
                                if ">" in url_part and "<" in url_part:
                                    url_text = url_part.split(">", 1)[1].split("<", 1)[0]
                                    company_url = url_text.strip()
                    except Exception:
                        # If parsing fails, use the original description
                        pass

                    converted_vacancy = {
                        "vacancy_id": f"wwr_{hash(vacancy['link'])}",
                        "vacancy_title": vacancy["title"],
                        "employer_name": vacancy["company"],
                        "vacancy_job_description": job_description,
                        "vacancy_url": vacancy["link"],
                        "company_url": company_url,
                        "vacancy_creation_date": datetime.now(),
                        "vacancy_country": vacancy["region"] or "Remote",
                        "vacancy_city": "",
                        "employer_logo_url": vacancy["logo_url"],
                        "salary_min": "Unspecified",
                        "salary_max": "",
                        "salary_currency": "Unknown",
                        "vacancy_type": vacancy["type"] or "Full-time",
                        "office_schedule": "Remote",
                        "listing_source": "WeWorkRemotely"
                    }
                    
                    # Add the vacancy regardless of logo URL (don't filter out jobs without logos)
                    converted_vacancies.append(converted_vacancy)

                except Exception as e:
                    logger.error(f"Error converting vacancy: {str(e)}")
                    continue
            
            return converted_vacancies
            
        except Exception as e:
            logger.error(f"Error collecting RSS feed: {str(e)}")
            return []
    
    def get_cached_vacancies(self, keyword="", limit=200):
        """
        Get cached RSS vacancies from database.
        
        Args:
            keyword (str): Optional keyword filter
            limit (int): Maximum number of results
            
        Returns:
            list: List of cached vacancy dictionaries
        """
        try:
            query = RSSFeedCache.query.filter_by(is_active=True)
            
            if keyword:
                keyword_filter = f"%{keyword.strip('%')}%"
                query = query.filter(
                    RSSFeedCache.vacancy_title.ilike(keyword_filter) |
                    RSSFeedCache.employer_name.ilike(keyword_filter)
                )
            
            vacancies = query.order_by(RSSFeedCache.created_at.desc()).limit(limit).all()
            
            # Convert to dictionary format for compatibility
            result = []
            for vacancy in vacancies:
                vacancy_dict = {
                    'vacancy_id': vacancy.vacancy_id,
                    'vacancy_title': vacancy.vacancy_title,
                    'employer_name': vacancy.employer_name,
                    'vacancy_job_description': vacancy.vacancy_job_description,
                    'vacancy_url': vacancy.vacancy_url,
                    'company_url': vacancy.company_url,
                    'vacancy_creation_date': vacancy.vacancy_creation_date,
                    'vacancy_country': vacancy.vacancy_country,
                    'vacancy_city': vacancy.vacancy_city,
                    'jobtags': [],
                    'employer_logo_url': vacancy.employer_logo_url,
                    'vacancy_status': 'Active',
                    'salary_min': vacancy.salary_min,
                    'salary_max': vacancy.salary_max,
                    'salary_currency': vacancy.salary_currency,
                    'vacancy_type': vacancy.vacancy_type,
                    'office_schedule': vacancy.office_schedule,
                    'listing_source': vacancy.listing_source,
                    'employer_banner_url': ""
                }
                result.append(vacancy_dict)
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting cached vacancies: {str(e)}")
            return []
    
    def clear_old_cache(self):
        """
        Clear old cached RSS entries to make room for fresh data.
        Only marks entries as inactive if they are older than 7 days to maintain stability.
        """
        try:
            from datetime import timedelta

            # Only mark entries as inactive if they are older than 7 days
            cutoff_date = datetime.now() - timedelta(days=7)

            # Count entries that will be marked inactive
            old_entries_count = RSSFeedCache.query.filter(
                RSSFeedCache.created_at < cutoff_date,
                RSSFeedCache.is_active == True
            ).count()

            if old_entries_count > 0:
                # Mark old RSS entries as inactive instead of deleting
                RSSFeedCache.query.filter(
                    RSSFeedCache.created_at < cutoff_date,
                    RSSFeedCache.is_active == True
                ).update({'is_active': False})

                db.session.commit()
                logger.info(f"Marked {old_entries_count} old RSS cache entries as inactive (older than 7 days)")
            else:
                logger.info("No old RSS cache entries found to clear")

        except Exception as e:
            logger.error(f"Error clearing old cache: {str(e)}")
            db.session.rollback()

    def collect_and_cache_google_jobs(self):
        """
        Collect jobs from Google Jobs RSS feed and cache them in database.
        Google uses a custom XML format, not standard RSS.

        Returns:
            int: Number of jobs cached
        """
        try:
            import requests
            import xml.etree.ElementTree as ET

            feed_url = "https://careers.google.com/jobs/feed.xml"
            logger.info(f"Fetching Google Jobs RSS feed from: {feed_url}")

            # Fetch the XML directly
            response = requests.get(feed_url, timeout=30)
            response.raise_for_status()

            # Parse XML
            root = ET.fromstring(response.content)
            jobs = root.findall('.//job')

            logger.info(f"Found {len(jobs)} jobs in Google RSS feed")

            cached_count = 0
            batch_size = 100  # Process in batches to avoid memory issues

            for i, job_element in enumerate(jobs):
                try:
                    # Parse the Google job element
                    vacancy_data = self._parse_google_job_element(job_element)
                    if vacancy_data:
                        # Check if already exists
                        existing = RSSFeedCache.query.filter_by(
                            vacancy_id=vacancy_data['vacancy_id']
                        ).first()

                        if existing:
                            # Update existing record
                            for key, value in vacancy_data.items():
                                setattr(existing, key, value)
                            existing.updated_at = datetime.now()
                        else:
                            # Create new record
                            new_vacancy = RSSFeedCache(**vacancy_data)
                            db.session.add(new_vacancy)

                        cached_count += 1

                        # Commit in batches to avoid memory issues
                        if (i + 1) % batch_size == 0:
                            db.session.commit()
                            logger.info(f"Processed {i + 1}/{len(jobs)} Google jobs...")

                except Exception as e:
                    logger.error(f"Error processing Google job element {i}: {str(e)}")
                    continue

            # Final commit for remaining jobs
            db.session.commit()
            logger.info(f"Successfully cached {cached_count} jobs from Google")
            return cached_count

        except Exception as e:
            logger.error(f"Error collecting Google jobs: {str(e)}")
            try:
                db.session.rollback()
            except:
                pass
            return 0

    def _parse_google_job_element(self, job_element):
        """
        Parse a Google Jobs XML job element into our vacancy format.

        Args:
            job_element: XML Element representing a job from Google's feed

        Returns:
            dict: Parsed vacancy data or None if parsing fails
        """
        try:
            # Extract basic information from XML elements
            title_elem = job_element.find('title')
            title = title_elem.text if title_elem is not None else ''

            description_elem = job_element.find('description')
            description = description_elem.text if description_elem is not None else ''

            url_elem = job_element.find('url')
            url = url_elem.text if url_elem is not None else ''

            # Extract Google-specific fields
            job_id_elem = job_element.find('jobid')
            job_id = job_id_elem.text if job_id_elem is not None else ''

            employer_elem = job_element.find('employer')
            employer = employer_elem.text if employer_elem is not None else 'Google'

            job_type_elem = job_element.find('jobtype')
            job_type = job_type_elem.text if job_type_elem is not None else 'FULL_TIME'

            # Extract location information
            primary_city = ""
            primary_country = ""

            # Check if job has Poland location
            has_poland_location = False

            locations_elem = job_element.find('locations')
            if locations_elem is not None:
                location_elems = locations_elem.findall('location')
                for location in location_elems:
                    country_elem = location.find('country')
                    if country_elem is not None and country_elem.text == 'Poland':
                        has_poland_location = True
                        city_elem = location.find('city')
                        if city_elem is not None:
                            primary_city = city_elem.text
                        primary_country = 'Poland'
                        break

            # Skip jobs that are not in Poland
            if not has_poland_location:
                return None

            # Extract remote work information
            is_remote_elem = job_element.find('isRemote')
            is_remote = is_remote_elem is not None and is_remote_elem.text == 'Yes'

            is_hybrid_elem = job_element.find('isHybrid')
            is_hybrid = is_hybrid_elem is not None and is_hybrid_elem.text == 'Yes'

            remote_elem = job_element.find('remote')
            remote_type = remote_elem.text if remote_elem is not None else 'onsite'

            office_schedule = "On-site"
            if is_remote:
                office_schedule = "Remote"
            elif is_hybrid:
                office_schedule = "Hybrid"
            elif remote_type == "remote":
                office_schedule = "Remote"

            # Create a unique job ID
            if job_id:
                unique_id = f"google_{job_id}"
            else:
                unique_id = f"google_{hash(url)}"

            vacancy_data = {
                'vacancy_id': unique_id,
                'vacancy_title': title,
                'employer_name': employer,
                'vacancy_job_description': description,
                'vacancy_url': url,
                'company_url': "https://careers.google.com/",
                'vacancy_creation_date': datetime.now(),
                'vacancy_country': primary_country,
                'vacancy_city': primary_city,
                'employer_logo_url': "https://www.gstatic.com/marketing-cms/assets/images/d5/dc/cfe9ce8b4425b410b49b7f2dd3f3/g.webp=s96-fcrop64=1,00000000ffffffff-rw",
                'salary_min': "Competitive",
                'salary_max': "",
                'salary_currency': "USD",
                'vacancy_type': job_type,
                'office_schedule': office_schedule,
                'listing_source': "Google",
                'is_active': True
            }

            return vacancy_data

        except Exception as e:
            logger.error(f"Error parsing Google job element: {str(e)}")
            return None

    def get_number_of_cached_jobs(self):
        """
        Get the number of jobs currently cached in the database.
        
        Returns:
            int: Number of cached jobs
        """
        try:
            return RSSFeedCache.query.filter_by(is_active=True).count()
        except Exception as e:
            logger.error(f"Error getting number of cached jobs: {str(e)}")
            return 0

    def update_all_feeds(self):
        """
        Update all RSS feeds. Called by the daily scheduler.
        
        Returns:
            dict: Update statistics
        """
        logger.info("Starting daily RSS feed update")
        
        stats = {
            'total_cached': 0,
            'errors': 0,
            'start_time': datetime.utcnow()
        }
        
        try:
            # Clear old cache
            self.clear_old_cache()
            
            # Update WeWorkRemotely feed
            wwr_count = self.collect_and_cache_we_work_remotely()
            stats['total_cached'] += wwr_count

            # Update Google Jobs feed
            google_count = self.collect_and_cache_google_jobs()
            stats['total_cached'] += google_count
            
            stats['end_time'] = datetime.utcnow()
            stats['duration'] = (stats['end_time'] - stats['start_time']).total_seconds()
            
            logger.info(f"RSS feed update completed. Cached {stats['total_cached']} jobs in {stats['duration']:.2f} seconds")
            
        except Exception as e:
            logger.error(f"Error during RSS feed update: {str(e)}")
            stats['errors'] += 1
        
        return stats


# Global RSS manager instance
rss_manager = RSSFeedManager()
