"""
Job Alert and Notification System
Handles user job alerts and email notifications for matching jobs.
"""

import re
import logging
from datetime import datetime, timedelta
from custom_libs.models import db, JobAlert, JobNotification, Vacancy, RSSFeedCache
from sqlalchemy import and_, or_
import redmail
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class NotificationSystem:
    """
    Manages job alerts and email notifications.
    """
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize the notification system with Flask app."""
        self.app = app
    
    def create_job_alert(self, email, keywords=None, location=None, remote_only=False, salary_min=None):
        """
        Create a new job alert for a user.
        
        Args:
            email (str): User's email address
            keywords (str): Comma-separated keywords
            location (str): Preferred location
            remote_only (bool): Only remote jobs
            salary_min (int): Minimum salary requirement
            
        Returns:
            JobAlert: Created job alert or None if failed
        """
        try:
            # Validate email
            if not self._is_valid_email(email):
                logger.error(f"Invalid email address: {email}")
                return None
            
            # Check if alert already exists for this email with same criteria
            existing_alert = JobAlert.query.filter_by(
                email=email,
                keywords=keywords,
                location=location,
                remote_only=remote_only,
                salary_min=salary_min,
                is_active=True
            ).first()
            
            if existing_alert:
                logger.info(f"Job alert already exists for {email}")
                return existing_alert
            
            # Create new alert
            new_alert = JobAlert(
                email=email,
                keywords=keywords,
                location=location,
                remote_only=remote_only,
                salary_min=salary_min,
                is_active=True
            )
            
            db.session.add(new_alert)
            db.session.commit()
            
            logger.info(f"Created job alert for {email}")
            return new_alert
            
        except Exception as e:
            logger.error(f"Error creating job alert: {str(e)}")
            db.session.rollback()
            return None
    
    def get_user_alerts(self, email):
        """
        Get all active job alerts for a user.
        
        Args:
            email (str): User's email address
            
        Returns:
            list: List of JobAlert objects
        """
        try:
            alerts = JobAlert.query.filter_by(email=email, is_active=True).all()
            return alerts
        except Exception as e:
            logger.error(f"Error getting user alerts: {str(e)}")
            return []
    
    def deactivate_alert(self, alert_id, email):
        """
        Deactivate a job alert.
        
        Args:
            alert_id (int): Alert ID
            email (str): User's email (for security)
            
        Returns:
            bool: Success status
        """
        try:
            alert = JobAlert.query.filter_by(id=alert_id, email=email).first()
            if alert:
                alert.is_active = False
                db.session.commit()
                logger.info(f"Deactivated alert {alert_id} for {email}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error deactivating alert: {str(e)}")
            db.session.rollback()
            return False
    
    def find_matching_jobs(self, alert, check_recent_only=True):
        """Find jobs matching alert criteria"""
        try:
            matching_jobs = []

            # Determine time filter based on check_recent_only parameter
            if check_recent_only:
                # For scheduled processing, only check jobs from last 24 hours
                time_filter = datetime.now() - timedelta(days=1)
            else:
                # For manual processing, check jobs from last 7 days
                time_filter = datetime.now() - timedelta(days=7)

            # Search in regular vacancies
            vacancy_query = Vacancy.query.filter(
                Vacancy.vacancy_status == 'Active',
                Vacancy.vacancy_creation_date >= time_filter
            )
            
            # Apply filters
            if alert.keywords:
                keywords = [kw.strip().lower() for kw in alert.keywords.split(',')]
                keyword_conditions = []
                for keyword in keywords:
                    keyword_conditions.extend([
                        Vacancy.vacancy_title.ilike(f'%{keyword}%'),
                        Vacancy.vacancy_job_description.ilike(f'%{keyword}%')
                    ])
                vacancy_query = vacancy_query.filter(or_(*keyword_conditions))
                
            if alert.location and not alert.remote_only:
                vacancy_query = vacancy_query.filter(
                    or_(
                        Vacancy.vacancy_city.ilike(f'%{alert.location}%'),
                        Vacancy.vacancy_country.ilike(f'%{alert.location}%')
                    )
                )
                
            if alert.remote_only:
                remote_keywords = ['remote', 'worldwide', 'anywhere', 'global', 'work from home', 'wfh']
                remote_conditions = []
                for keyword in remote_keywords:
                    remote_conditions.extend([
                        Vacancy.vacancy_country.ilike(f'%{keyword}%'),
                        Vacancy.vacancy_city.ilike(f'%{keyword}%'),
                        Vacancy.office_schedule.ilike(f'%{keyword}%')
                    ])
                vacancy_query = vacancy_query.filter(or_(*remote_conditions))
                
            if alert.salary_min:
                vacancy_query = vacancy_query.filter(Vacancy.salary_min >= alert.salary_min)

            # Get matching vacancies
            vacancies = vacancy_query.with_entities(
                Vacancy.vacancy_id,
                Vacancy.vacancy_title,
                Vacancy.vacancy_country,
                Vacancy.vacancy_city,
                Vacancy.salary_min,
                Vacancy.salary_max,
                Vacancy.salary_currency,
                Vacancy.office_schedule
            ).limit(10).all()
            
            # Add matching vacancies that haven't been notified
            for vacancy in vacancies:
                if not JobNotification.query.filter_by(
                    alert_id=alert.id,
                    vacancy_id=str(vacancy.vacancy_id)
                ).first():
                    matching_jobs.append({
                        'vacancy_id': str(vacancy.vacancy_id),
                        'vacancy_title': vacancy.vacancy_title,
                        'employer_name': 'Unknown',
                        'vacancy_country': vacancy.vacancy_country,
                        'vacancy_city': vacancy.vacancy_city,
                        'salary_min': str(vacancy.salary_min) if vacancy.salary_min else None,
                        'salary_max': str(vacancy.salary_max) if vacancy.salary_max else None,
                        'salary_currency': vacancy.salary_currency,
                        'office_schedule': vacancy.office_schedule,
                        'source': 'Canvider',
                        'vacancy_url': f"https://workloupe.com/apply/{vacancy.vacancy_id}"
                    })
            
            # Search in RSS cache
            rss_query = RSSFeedCache.query.filter(
                RSSFeedCache.is_active == True,
                RSSFeedCache.created_at >= time_filter
            )
            
            if alert.keywords:
                rss_keyword_conditions = []
                for keyword in keywords:
                    rss_keyword_conditions.extend([
                        RSSFeedCache.vacancy_title.ilike(f'%{keyword}%'),
                        RSSFeedCache.vacancy_job_description.ilike(f'%{keyword}%')
                    ])
                rss_query = rss_query.filter(or_(*rss_keyword_conditions))
            
            # Add matching RSS jobs that haven't been notified
            for rss_vacancy in rss_query.limit(5).all():
                if not JobNotification.query.filter_by(
                    alert_id=alert.id,
                    vacancy_id=rss_vacancy.vacancy_id
                ).first():
                    matching_jobs.append({
                        'vacancy_id': str(rss_vacancy.vacancy_id),
                        'vacancy_title': rss_vacancy.vacancy_title,
                        'employer_name': rss_vacancy.employer_name,
                        'vacancy_country': rss_vacancy.vacancy_country,
                        'vacancy_city': rss_vacancy.vacancy_city,
                        'salary_min': str(rss_vacancy.salary_min) if rss_vacancy.salary_min else None,
                        'salary_max': str(rss_vacancy.salary_max) if rss_vacancy.salary_max else None,
                        'salary_currency': rss_vacancy.salary_currency,
                        'office_schedule': rss_vacancy.office_schedule,
                        'source': rss_vacancy.listing_source,
                        'vacancy_url': rss_vacancy.vacancy_url
                    })
            
            return matching_jobs
            
        except Exception as e:
            logger.error(f"Error finding matching jobs: {str(e)}")
            return []
    
    def send_job_alert_email(self, alert, matching_jobs):
        """
        Send email notification for matching jobs.
        
        Args:
            alert (JobAlert): Job alert object
            matching_jobs (list): List of matching job dictionaries
            
        Returns:
            bool: Success status
        """
        try:
            if not matching_jobs:
                return True
            
            # Prepare email content
            subject = f"New Job Matches for Your Alert - {len(matching_jobs)} Jobs Found"
            
            # Create email body
            email_body = f"""
Dear Job Seeker,

We found {len(matching_jobs)} new job(s) that match your alert criteria:

Keywords: {alert.keywords or 'Any'}
Location: {alert.location or 'Any'}
Remote Only: {'Yes' if alert.remote_only else 'No'}
Minimum Salary: {alert.salary_min or 'Not specified'}

Here are the matching jobs:

"""
            
            for job in matching_jobs:
                location = f"{job['vacancy_city']}, {job['vacancy_country']}" if job['vacancy_city'] else job['vacancy_country']
                salary = f"{job['salary_min']} - {job['salary_max']} {job['salary_currency']}" if job['salary_min'] and job['salary_max'] else "Not specified"
                
                email_body += f"""
• {job['vacancy_title']} at {job['employer_name']}
  Location: {location}
  Salary: {salary}
  Work Mode: {job['office_schedule'] or 'Not specified'}
  Apply: {"https://workloupe.com/apply/" + job['vacancy_id'] if job['source'] == 'Canvider' else job['vacancy_url']}

"""
            
            email_body += """
Best regards,
The Workloupe Team

To unsubscribe from these alerts, please visit: https://workloupe.com/job-alerts
"""
            
            # Send email
            sender = os.getenv("MAIL_USERNAME")
            r_email = redmail.EmailSender(
                host=os.getenv("SMTP_MAIL_HOST"),
                port=os.getenv("SMTP_MAIL_PORT"),
                username=sender,
                password=os.getenv("MAIL_PASSWORD"),
            )
            
            r_email.connect()
            r_email.send(
                subject=subject,
                sender=f"Workloupe Job Alerts <{sender}>",
                text=email_body,
                receivers=alert.email,
            )
            
            # Record notifications
            for job in matching_jobs:
                notification = JobNotification(
                    alert_id=alert.id,
                    vacancy_id=str(job['vacancy_id']),
                    sent_at=datetime.utcnow()
                )
                db.session.add(notification)
            
            # Update last notification time
            alert.last_notification_sent = datetime.utcnow()
            db.session.commit()
            
            logger.info(f"Sent job alert email to {alert.email} with {len(matching_jobs)} jobs")
            return True
            
        except Exception as e:
            logger.error(f"Error sending job alert email: {str(e)}")
            db.session.rollback()
            return False
    
    def process_all_alerts(self):
        """
        Process all active job alerts and send notifications.
        Called by the daily scheduler.
        
        Returns:
            dict: Processing statistics
        """
        logger.info("Starting job alert processing")
        
        stats = {
            'alerts_processed': 0,
            'emails_sent': 0,
            'errors': 0,
            'start_time': datetime.utcnow()
        }
        
        try:
            # Get all active alerts that haven't been notified in the last 24 hours
            yesterday = datetime.utcnow() - timedelta(days=1)
            alerts = JobAlert.query.filter(
                and_(
                    JobAlert.is_active == True,
                    or_(
                        JobAlert.last_notification_sent == None,
                        JobAlert.last_notification_sent <= yesterday
                    )
                )
            ).all()
            
            for alert in alerts:
                try:
                    # Use check_recent_only=True for scheduled processing (only last 24 hours)
                    matching_jobs = self.find_matching_jobs(alert, check_recent_only=True)
                    if matching_jobs:
                        success = self.send_job_alert_email(alert, matching_jobs)
                        if success:
                            stats['emails_sent'] += 1
                        else:
                            stats['errors'] += 1

                    stats['alerts_processed'] += 1
                    
                except Exception as e:
                    logger.error(f"Error processing alert {alert.id}: {str(e)}")
                    stats['errors'] += 1
            
            stats['end_time'] = datetime.utcnow()
            stats['duration'] = (stats['end_time'] - stats['start_time']).total_seconds()
            
            logger.info(f"Job alert processing completed. Processed {stats['alerts_processed']} alerts, sent {stats['emails_sent']} emails in {stats['duration']:.2f} seconds")
            
        except Exception as e:
            logger.error(f"Error during job alert processing: {str(e)}")
            stats['errors'] += 1
        
        return stats

    def process_alerts_for_user(self, email):
        """
        Process job alerts for a specific user (for manual testing).

        Args:
            email (str): User email address

        Returns:
            dict: Processing statistics
        """
        logger.info(f"Processing job alerts for user: {email}")

        stats = {
            'alerts_processed': 0,
            'emails_sent': 0,
            'errors': 0,
            'start_time': datetime.utcnow()
        }

        try:
            # Get all active alerts for this user
            alerts = JobAlert.query.filter(
                JobAlert.email == email,
                JobAlert.is_active == True
            ).all()

            for alert in alerts:
                try:
                    # Use check_recent_only=False for manual processing (last 7 days)
                    matching_jobs = self.find_matching_jobs(alert, check_recent_only=False)
                    if matching_jobs:
                        success = self.send_job_alert_email(alert, matching_jobs)
                        if success:
                            stats['emails_sent'] += 1
                        else:
                            stats['errors'] += 1

                    stats['alerts_processed'] += 1

                except Exception as e:
                    logger.error(f"Error processing alert {alert.id}: {str(e)}")
                    stats['errors'] += 1

            stats['end_time'] = datetime.utcnow()
            stats['duration'] = (stats['end_time'] - stats['start_time']).total_seconds()

            logger.info(f"User alert processing completed. Processed {stats['alerts_processed']} alerts, sent {stats['emails_sent']} emails in {stats['duration']:.2f} seconds")

        except Exception as e:
            logger.error(f"Error during user alert processing: {str(e)}")
            stats['errors'] += 1

        return stats

    def _is_valid_email(self, email):
        """Validate email address."""
        email_regex = re.compile(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
        return bool(email_regex.match(email))


# Global notification system instance
notification_system = NotificationSystem()
