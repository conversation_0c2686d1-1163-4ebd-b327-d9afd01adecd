:root {
    /* Primary Theme Colors */
    --bs-primary: #8A4962;
    /* Primary color */
    --bs-secondary: #3D1D5A;
    /* Secondary color */
    --bs-success: #1C501C;
    /* Success color */

    /* Light Variants */
    --bs-primary-bg-subtle: #FAD2E1;
    /* Light primary background */
    --bs-secondary-bg-subtle: #DBC5F0;
    /* Light secondary background */
    --bs-success-bg-subtle: #C5DDBE;
    /* Light success background */

    /* Dark Variants */
    --bs-primary-bg-dark: #6A2C3A;
    --bs-secondary-bg-dark: #2A0F2A;
    --bs-success-bg-dark: #0D3D0D;

    /* Very Light Variants */
    --bs-primary-bg: #F8E4EC;
    /* Very light primary background */
    --bs-secondary-bg: #ECE1F6;
    /* Very light secondary background */
    --bs-success-bg: #DFEBDC;
    /* Very light success background */

    /* Light Theme Variables */
    --bs-body-bg: #fcfcfc;
    --bs-body-color: #333333;
    --bs-light-gray: #f8f9fa;
    --bs-card-bg: #ffffff;
    --bs-border-color: #e0e0e0;
    --bs-text-muted: #666666;
    --bs-navbar-bg: #ffffff;
    --bs-navbar-color: #333333;
    --bs-shadow: rgba(0, 0, 0, 0.1);
    --bs-shadow-hover: rgba(0, 0, 0, 0.15);
}

/* Dark Theme Variables */
[data-theme="dark"] {
    --bs-body-bg: #1a1a1a;
    --bs-body-color: #e0e0e0;
    --bs-light-gray: #2d2d2d;
    --bs-card-bg: #2d2d2d;
    --bs-border-color: #404040;
    --bs-text-muted: #a0a0a0;
    --bs-navbar-bg: #2d2d2d;
    --bs-navbar-color: #e0e0e0;
    --bs-shadow: rgba(0, 0, 0, 0.3);
    --bs-shadow-hover: rgba(0, 0, 0, 0.4);

    /* Adjust primary colors for dark theme */
    --bs-primary-bg-subtle: #4a2d3a;
    --bs-secondary-bg-subtle: #2d1a3a;
    --bs-success-bg-subtle: #1a3a1a;

    --bs-primary-bg: #3a2029;
    --bs-secondary-bg: #2a1529;
    --bs-success-bg: #152915;
}


body, html {
    overflow-x: clip !important;
    width: 100vw !important;
    font-family: 'Montserrat' !important;
    background-color: var(--bs-body-bg) !important;
    color: var(--bs-body-color) !important;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark Theme Styles */
.dark-theme-toggle {
    background: none;
    border: 2px solid var(--bs-border-color);
    border-radius: 25px;
    padding: 0.5rem 1rem;
    color: var(--bs-body-color);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.dark-theme-toggle:hover {
    background: var(--bs-primary);
    color: white;
    border-color: var(--bs-primary);
}

.dark-theme-toggle i {
    font-size: 1.1rem;
}

/* Update existing styles to use CSS variables */
.modern-card,
.info-card,
.contact-card-modern,
.value-card,
.job-card-modern,
.job-card-company {
    background: var(--bs-card-bg) !important;
    border-color: var(--bs-border-color) !important;
    color: var(--bs-body-color) !important;
    box-shadow: 0 2px 10px var(--bs-shadow) !important;
}

.modern-card:hover,
.info-card:hover,
.value-card:hover,
.job-card-modern:hover,
.job-card-company:hover {
    box-shadow: 0 8px 25px var(--bs-shadow-hover) !important;
}

.navbar-light {
    background-color: var(--bs-navbar-bg) !important;
    color: var(--bs-navbar-color) !important;
}

.navbar-light .navbar-brand,
.navbar-light .nav-link {
    color: var(--bs-navbar-color) !important;
}

.text-muted {
    color: var(--bs-text-muted) !important;
}

.bg-light {
    background-color: var(--bs-light-gray) !important;
}

.border {
    border-color: var(--bs-border-color) !important;
}

.form-control {
    background-color: var(--bs-card-bg) !important;
    border-color: var(--bs-border-color) !important;
    color: var(--bs-body-color) !important;
}

.form-control:focus {
    background-color: var(--bs-card-bg) !important;
    border-color: var(--bs-primary) !important;
    color: var(--bs-body-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(138, 73, 98, 0.25) !important;
}

.modal-content {
    background-color: var(--bs-card-bg) !important;
    color: var(--bs-body-color) !important;
}

.dropdown-menu {
    background-color: var(--bs-card-bg) !important;
    border-color: var(--bs-border-color) !important;
}

.dropdown-item {
    color: var(--bs-body-color) !important;
}

.dropdown-item:hover {
    background-color: var(--bs-light-gray) !important;
}

/* Dark theme specific adjustments */
[data-theme="dark"] .company-header-overlay {
    background: linear-gradient(135deg, rgba(138, 73, 98, 0.9) 0%, rgba(61, 29, 90, 0.95) 100%);
}

[data-theme="dark"] .about-hero-overlay {
    background: linear-gradient(135deg, rgba(138, 73, 98, 0.9) 0%, rgba(61, 29, 90, 0.95) 100%);
}

[data-theme="dark"] .contact-item,
[data-theme="dark"] .social-link-modern,
[data-theme="dark"] .location-item,
[data-theme="dark"] .contact-method-modern {
    background: var(--bs-light-gray) !important;
}

[data-theme="dark"] .gallery-overlay {
    background: rgba(0, 0, 0, 0.7);
}

[data-theme="dark"] .no-contact-info,
[data-theme="dark"] .no-social-info,
[data-theme="dark"] .no-location-info {
    background: var(--bs-light-gray) !important;
}

[data-theme="dark"] .section-title,
[data-theme="dark"] .contact-title,
[data-theme="dark"] .about-hero-title,
[data-theme="dark"] .hero-title {
    color: var(--bs-body-color) !important;
}

[data-theme="dark"] .contact-value,
[data-theme="dark"] .social-name,
[data-theme="dark"] .location-name,
[data-theme="dark"] .job-title,
[data-theme="dark"] .value-title {
    color: var(--bs-body-color) !important;
}

[data-theme="dark"] .about-text,
[data-theme="dark"] .mission-text,
[data-theme="dark"] .value-description,
[data-theme="dark"] .contact-subtitle {
    color: var(--bs-text-muted) !important;
}

/* Hero Search Bar Styles */
.hero-search-container {
    max-width: 600px;
    margin: 0 auto;
}

.hero-search-form {
    position: relative;
}

.hero-search-wrapper {
    position: relative;
}

.hero-search-input-group {
    position: relative;
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 50px;
    padding: 0.75rem 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.hero-search-input-group:focus-within {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--bs-primary);
}

.hero-search-icon {
    color: var(--bs-primary);
    font-size: 1.25rem;
    margin-right: 1rem;
}

.hero-search-input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-size: 1.1rem;
    color: #333;
    font-weight: 500;
}

.hero-search-input::placeholder {
    color: #666;
    font-weight: 400;
}

.hero-search-btn {
    background: var(--bs-primary);
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    margin-left: 1rem;
}

.hero-search-btn:hover {
    background: var(--bs-primary-bg-dark);
    transform: scale(1.05);
}

.hero-search-suggestions {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 1.5rem;
}

.search-suggestion {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-suggestion:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Dark theme adjustments for search */
[data-theme="dark"] .hero-search-input-group {
    background: rgba(45, 45, 45, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .hero-search-input-group:focus-within {
    background: rgba(45, 45, 45, 1);
    border-color: var(--bs-primary);
}

[data-theme="dark"] .hero-search-input {
    color: #e0e0e0;
}

[data-theme="dark"] .hero-search-input::placeholder {
    color: #a0a0a0;
}

/* Responsive design for search */
@media (max-width: 768px) {
    .hero-search-container {
        max-width: 100%;
        padding: 0 1rem;
    }

    .hero-search-input-group {
        padding: 0.5rem 1rem;
    }

    .hero-search-input {
        font-size: 1rem;
    }

    .hero-search-btn {
        width: 40px;
        height: 40px;
        margin-left: 0.5rem;
    }

    .hero-search-suggestions {
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .search-suggestion {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
}

.limited-width-content {
    max-width: 1200px !important;
    margin: auto !important;
}


.less-limited-width-content {
    max-width: 1600px !important;
    margin: auto !important;
}

.job-icon {
    max-width: 40px;
    max-height: 40px;
}

.big-button {
    height: 100px;
}

.space-from-top {
    margin-top: 10vw !important;
}

.space-from-bottom {
    margin-bottom: 6vh !important;
}

.space-from-bottom-xl {
    margin-bottom: 10vw !important;
}


.stretch-text {
    text-align: justify;
    letter-spacing: 3px;
}

.footer-links {
    font-size: 14px !important;
    margin-top: 4px;
    margin-bottom: 4px;
    display: block;
    color: #7a7a7a;
    text-decoration: none;
}


.learn-links {
    font-size: 14px !important;
    margin-top: 4px;
    margin-bottom: 4px;
    display: block;
    color: #0e0e0e;
    text-decoration: none;
}


.main-link-item {
    text-decoration: none;
    padding-bottom: 8px !important;
    color: #000 !important;
    font-size: 24px !important;
    display: block;
}

#footer {
    background-color: #f6f6f6;
}

.carousel-indicators {
    list-style-type: none;
}

.carousel-item {
    overflow: hidden;
    width: 100%;
}

#demo-req-form {
    max-width: 600px !important;
}

#demo-request-text {
    padding-top: 12vh !important;
    padding-bottom: 8vh !important;
}


.pricing-card:hover {
    box-shadow: 0px 0px 10px #6a6a6a !important;
}

.feature-card {
    border-radius: 8px;
    border-style: solid;
    border-color: #FFF;
    border-width: 2px;
    background-color: #fff;
    column-fill: auto;
    position: relative;
    box-shadow: 0px 0px 15px #7a7a7a !important;
}

#transition-from-red {
    background: url(./Static/Illustrations/arkaplan_2.svg);
    background-size: cover;
    background-position: center bottom;
}

#transition-to-red {
    background: url(./Static/Illustrations/arkaplan.svg);
    background-size: cover;
}

#transition-to-gray {
    background: url(./Static/Illustrations/arkaplan_3.svg);
    background-size: cover;
}

#transition-from-gray {
    background: url(./Static/Illustrations/arkaplan_3.svg);
    background-size: cover;
    transform: scaleY(-1);
}


.lrn-more-btn {
    border-top-left-radius: 0%;
    border-top-right-radius: 0%;
    position: absolute;
    bottom: 0;
    width: 100%;
}


.feature-info-card {
    background-color: #ffffff;
    border-radius: 24px;
    padding: 10px;
    margin: 10px;
    margin-bottom: 30px;
}

.landing-info-card {
    background-color: #f8eeee;
    border-radius: 24px;
    padding: 10px;
    margin: 10px;
    margin-bottom: 30px;
}

.landing-info-card-alt {
    background-color: #F0F0F0;
    border-radius: 24px;
    padding: 10px;
    margin: 10px;
    margin-bottom: 30px;
}

.feature-info-card-text {
    max-width: 450px;
    color: #000;
}

.demo-img {
    max-height: 300px;
}

.client-logo {
    max-height: 80px !important;
    max-width: 120px !important;
    width: 100%;
}

#employer-message {
    margin-top: 5%;
    margin-bottom: 5%;
    margin-left: 2%;
    margin-right: 2%;
}

.employer-banner {
    background-size: cover;
    background-position: center;
    height: 20vw;
    width: auto;
    margin-bottom: -4vw;
}

.employer-icon {
    height: 100%;
    width: 100%;
    max-height: 200px;
    max-width: 200px;
    min-width: 100px;
    min-height: 100px;
}

.employer-icon-sm {
    min-height: 40px;
    min-width: 40px;
}


.employer-icon-md {
    min-height: 60px;
    min-width: 60px;
}


.employer-icon-card {
    min-height: 40px;
    min-width: 40px;
    max-height: 100px;
    max-width: 100px;
}

.gallery-item {
    height: 100%;
    width: 100%;
    max-height: 200px;
    min-width: 100px;
    min-height: 100px;
    filter: blur(3px) grayscale(0.7);
    object-fit: cover;
}

.gallery-button {
    height: 100px;
    border: #000;
}

.floating-btn {
    margin-top: -66px;
    margin-bottom: -66px;
    z-index: 99;
}

.listing {
    opacity: 0;
    visibility: hidden;
    height: 0px;
    transition: opacity 0.5s ease, visibility 0.5s ease, height 0s ease;
    background-color: white;
}

.listing * {
    max-height: 100% !important;
    overflow-y: hidden;
}

.listing.visible {
    opacity: 1;
    /* Fade in */
    height: fit-content;
    visibility: visible;
    /* Make it interactive */
}
.listing-pointer {
    background-color: transparent;
    transition: background-color 0.5s ease, border 0.5s ease;
    border-right: 2px solid transparent;
}

.listing-pointer.active {
    background-color: var(--bs-light-gray) !important;
    border-right: var(--bs-primary) 2px solid;
}

.sm-text {
    font-size: smaller;
}

.xs-text {
    font-size: x-small;
}

.mini-jobs {
    background-color: #ffffff;
    border-radius: 8px;
    transition: 0.1s ease;
    border: 1px solid #ffffff;
    max-width: 600px;
    margin-top: 4px;
    margin-bottom: 6px;
}

.mini-jobs:hover {
    border: 1px solid #b41e1e;

}


@media (min-width: 560px) {
    .mini-jobs {
        width: 80%;
    }
}


@media (min-width: 780px) {
    .mini-jobs {
        width: 70%;
    }
}


@media (min-width: 1000px) {
    .mini-jobs {
        width: 60%;
    }
}

.gradient-bg {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.job-card {
    transition: transform 0.2s ease-in-out;
    border-left: 4px solid var(--bs-primary-bg-subtle);
}

.job-card:hover {
    transform: translateY(-3px);
}

.company-logo {
    max-height: 50px;
    width: auto;
    object-fit: contain;
}

.salary-highlight {
    color: #1C501C;
    font-weight: 600;
}

.badge-remote {
    background-color: #e3f2fd;
    color: #0d6efd;
}

.section-heading {
    font-size: 2rem;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 0.5rem;
    margin-bottom: 2rem;
}

.employer-card {
    background: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.2s ease;
}


@media (max-width: 767px) {
    .hero-img {
        max-width: 300px;
        margin: auto;
    }
}

.employer-card:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}


/* Altering the defaults of the bootstrap */

.text-primary {
    color: var(--bs-primary) !important;
}
a.text-primary:active, a.text-primary:hover {
    color: var(--bs-primary-bg-dark) !important;
}

.color-primary {
    color: var(--bs-primary) !important;
}

.bg-light-1 {
    background-color: var(--bs-primary-bg) !important;
}

.bg-light-2 {
    background-color: var(--bs-secondary-bg) !important;
}

.btn-primary {
    background-color: var(--bs-primary) !important;
    border-color: var(--bs-primary) !important;
}

.btn-primary:focus, .btn-primary:active, .btn-primary:hover {
    outline: none !important;
    box-shadow: none;
    background-color: var(--bs-primary-bg-dark) !important;
}

.btn-secondary {
    background-color: var(--bs-secondary) !important;
    border-color: var(--bs-secondary) !important;
}

.btn-secondary:focus, .btn-secondary:active, .btn-secondary:hover {
    outline: none !important;
    box-shadow: none;
    background-color: var(--bs-secondary-bg-dark) !important;
}

.btn-outline-primary {
    color: var(--bs-primary) !important;
    border-color: var(--bs-primary) !important;
}

.btn-outline-primary:focus, .btn-outline-primary:active, .btn-outline-primary:hover {
    outline: none !important;
    box-shadow: none;
    background-color: var(--bs-primary-bg-dark) !important;
    color: white !important;
}

.border-primary {
    border-color: var(--bs-primary) !important;
}


/* CSS Additions/Modifications */
#search-div {
    background: #fff;
    border: 1px solid #e0e0e0;
    transition: all 0.2s ease;
}

#search-div:focus-within {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(138, 73, 98, 0.15);
}

/* Enhanced Form Controls */
.form-control.border-2 {
    border-width: 2px !important;
    transition: all 0.3s ease;
}

.form-control.border-2:focus {
    border-color: var(--bs-primary) !important;
    box-shadow: 0 0 0 0.2rem rgba(138, 73, 98, 0.15) !important;
}

/* Loading Indicator */
.htmx-indicator {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.htmx-request .htmx-indicator {
    opacity: 1;
}

.htmx-request.htmx-indicator {
    opacity: 1;
}

/* Location Autocomplete Dropdown */
.location-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-top: none;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: none;
    max-height: 200px;
    overflow-y: auto;
}

.location-suggestion-item {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.location-suggestion-item:hover {
    background-color: var(--bs-primary-bg-subtle);
}

.location-suggestion-item:last-child {
    border-bottom: none;
}

/* Modern Card Styles */
.modern-card {
    background: white;
    border: 1px solid #e8ecef;
    transition: all 0.3s ease;
    overflow: hidden;
}

.modern-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* Job Card Mini Styles */
.job-card-mini {
    background: white;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
}

.job-card-mini:hover {
    border-color: var(--bs-primary-bg-subtle);
    background: var(--bs-primary-bg);
}

.job-card-mini.active {
    border-color: var(--bs-primary);
    background: var(--bs-primary-bg-subtle);
    box-shadow: 0 4px 12px rgba(138, 73, 98, 0.15);
}

/* Job Description Content */
.job-description-content {
    line-height: 1.7;
}

.job-description-content h1,
.job-description-content h2,
.job-description-content h3,
.job-description-content h4,
.job-description-content h5,
.job-description-content h6 {
    color: var(--bs-primary);
    margin-top: 1.5rem;
    margin-bottom: 1rem;
}

.job-description-content ul,
.job-description-content ol {
    padding-left: 1.5rem;
    margin-bottom: 1rem;
}

.job-description-content li {
    margin-bottom: 0.5rem;
}

/* Job Sidebar */
.job-sidebar {
    border: 1px solid #e8ecef;
}

.fact-item {
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #f0f0f0;
}

.fact-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.employer-card {
    background: white;
    border: 1px solid #eee;
    border-radius: 8px;
    transition: box-shadow 0.2s ease;
    overflow: hidden;
}

.employer-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.company-logo-wrapper {
    padding: 1rem;
    border-bottom: 1px solid #f5f5f5;
    background: #fafafa;
}

.employer-icon-md {
    height: 60px;
    width: 60px;
    object-fit: contain;
    padding: 4px;
    background: white;
    border-radius: 6px;
    border: 1px solid #eee;
}

.company-details {
    padding: 1rem;
    font-size: 1.3rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f5f5f5;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    color: #666;
    font-weight: 500;
    font-size: 0.8rem;
}

.detail-value {
    color: #333;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 1rem;
}

.open-positions {
    background: var(--bs-primary-bg-subtle);
    color: var(--bs-primary);
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Add these CSS styles */
/* Company Detail Page Styles */
.company-header {
    background-position: center;
    background-size: cover;
    height: 200px;
    position: relative;
}

.company-info-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    position: relative;
    margin-top: -60px;
    padding-top: 80px;
}

.company-logo-card {
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 80px;
    border-radius: 8px;
    background: white;
    padding: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #eee;
}

.detail-section {
    border-bottom: 1px solid #eee;
    padding: 2rem 0;
}

.detail-section:last-child {
    border-bottom: none;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.contact-method i {
    font-size: 1.25rem;
    color: var(--bs-primary);
}

.contact-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--bs-primary);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.gallery-item {
    aspect-ratio: 1;
    object-fit: cover;
    border-radius: 8px;
    transition: transform 0.2s ease;
}

.gallery-item:hover {
    transform: scale(1.03);
}

.job-listing {
    padding: 1.5rem;
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 1rem;
    transition: border-color 0.2s ease;
}

.job-listing:hover {
    border-color: var(--bs-primary);
}

.social-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    text-decoration: none;
    padding: 0.5rem;
    border-radius: 6px;
}

.social-link:hover {
    background: var(--bs-primary-bg-subtle);
    color: var(--bs-primary);
}

/* Gallery Modal Adjustments */
.modal-gallery .carousel-item {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    max-height: 70vh;
}

.modal-gallery img {
    object-fit: contain;
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
}

/* Existing Styles from Previous Versions */
.limited-width-content {
    max-width: 1200px !important;
    margin: auto !important;
}

.employer-card {
    background: white;
    border: 1px solid #eee;
    border-radius: 8px;
    transition: box-shadow 0.2s ease;
    overflow: hidden;
}

.employer-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* Modern Employer Cards */
.employer-card-modern {
    background: white;
    border: 1px solid #e8ecef;
    transition: all 0.3s ease;
    overflow: hidden;
}

.employer-card-modern:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
}

.detail-item-modern {
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #f0f0f0;
}

.detail-item-modern:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

/* Modern Index Page Styles */

/* Hero Section */
.modern-hero-bg {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
    position: relative;
    overflow: hidden;
}

.modern-hero-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23ffffff" stroke-width="0.5" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.1;
}

.min-vh-50 {
    min-height: 50vh;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.hero-gradient-text {
    background: linear-gradient(135deg, var(--bs-primary-bg-subtle) 0%, var(--bs-secondary-bg-subtle) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.hero-subtitle {
    font-size: 1.25rem;
    line-height: 1.6;
    color: #6c757d;
    font-weight: 400;
}

.hero-actions {
    gap: 1rem;
}

.btn-hero-primary {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
    border: none;
    color: white;
    font-weight: 600;
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(138, 73, 98, 0.3);
}

.btn-hero-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(138, 73, 98, 0.4);
    color: white;
}

.btn-hero-secondary {
    background: white;
    border: 2px solid var(--bs-primary);
    color: var(--bs-primary);
    font-weight: 600;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.btn-hero-secondary:hover {
    background: var(--bs-primary);
    color: white;
    transform: translateY(-2px);
}

.hero-stats {
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--bs-primary-bg-subtle);
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

/* Hero Image */
.hero-image-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-image {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.1));
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    background: white;
    border-radius: 12px;
    padding: 0.75rem 1rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--bs-primary);
    animation: float 3s ease-in-out infinite;
}

.floating-card-1 {
    top: 20%;
    right: 10%;
    animation-delay: 0s;
}

.floating-card-2 {
    bottom: 30%;
    left: 5%;
    animation-delay: 1s;
}

.floating-card-3 {
    top: 60%;
    right: 20%;
    animation-delay: 2s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Latest Opportunities Section */
.latest-opportunities {
    background: #fcfcfc;
    position: relative;
}

.section-header {
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.gradient-text {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 2rem;
}

.section-divider {
    width: 60px;
    height: 4px;
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
    margin: 0 auto;
    border-radius: 2px;
}

/* Jobs Grid */
.jobs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

@media (max-width: 768px) {
    .jobs-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* Modern Job Cards */
.job-card-modern {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #e8ecef;
    position: relative;
    height: 100%;
}

.job-card-modern:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: var(--bs-primary-bg-subtle);
}

.job-link {
    text-decoration: none;
    color: inherit;
    display: block;
    height: 100%;
}

.job-card-content {
    padding: 1.5rem;
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Job Header */
.job-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.company-logo-wrapper-0 {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.company-logo-modern {
    width: 50px;
    height: 50px;
    object-fit: contain;
    border-radius: 8px;
}

.job-source-badge {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Job Info */
.job-info {
    margin-bottom: 1rem;
}

.job-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.company-name {
    color: #6c757d;
    font-size: 1rem;
    font-weight: 500;
    margin: 0;
}

/* Job Tags */
.job-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.job-tag {
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    border: 1px solid transparent;
}

.job-tag-primary {
    background: var(--bs-primary-bg-subtle);
    color: var(--bs-primary);
    border-color: var(--bs-primary-bg-subtle);
}

.job-tag-secondary {
    background: #e3f2fd;
    color: #1976d2;
    border-color: #e3f2fd;
}

.job-tag-accent {
    background: #f3e5f5;
    color: #7b1fa2;
    border-color: #f3e5f5;
}

/* Job Footer */
.job-footer {
    margin-top: auto;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    padding-top: 1rem;
    border-top: 1px solid #f0f0f0;
}

.salary-info {
    flex: 1;
}

.salary-range {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--bs-success);
    line-height: 1;
}

.salary-period {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.salary-competitive {
    font-size: 1rem;
    font-weight: 600;
    color: #6c757d;
}

.job-date {
    font-size: 0.8rem;
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Load More Button */
.btn-load-more {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(138, 73, 98, 0.3);
}

.btn-load-more:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(138, 73, 98, 0.4);
    color: white;
}

/* No Jobs Message */
.no-jobs-message {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 16px;
    border: 1px solid #e8ecef;
}

.no-jobs-icon {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 1.5rem;
}

.no-jobs-message h3 {
    color: #6c757d;
    margin-bottom: 1rem;
}

.no-jobs-message p {
    color: #adb5bd;
    margin-bottom: 2rem;
}

/* Responsive Design */
@media (max-width: 992px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        justify-content: center;
        gap: 2rem;
    }

    .floating-elements {
        display: none;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
        text-align: center;
    }

    .hero-subtitle {
        text-align: center;
        font-size: 1.1rem;
    }

    .hero-actions {
        justify-content: center;
        flex-direction: column;
        align-items: center;
    }

    .btn-hero-primary,
    .btn-hero-secondary {
        width: 100%;
        max-width: 300px;
    }

    .section-title {
        font-size: 2rem;
    }

    .jobs-grid {
        grid-template-columns: 1fr;
    }

    .job-card-content {
        padding: 1.25rem;
    }

    .job-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 1.75rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .job-card-content {
        padding: 1rem;
    }

    .company-logo-wrapper {
        width: 50px;
        height: 50px;
    }

    .company-logo-modern {
        width: 40px;
        height: 40px;
    }

    .job-title {
        font-size: 1.1rem;
    }
}

/* Animation Enhancements */
@media (prefers-reduced-motion: no-preference) {
    .job-card-modern {
        animation: fadeInUp 0.6s ease-out;
    }

    .job-card-modern:nth-child(1) { animation-delay: 0.1s; }
    .job-card-modern:nth-child(2) { animation-delay: 0.2s; }
    .job-card-modern:nth-child(3) { animation-delay: 0.3s; }
    .job-card-modern:nth-child(4) { animation-delay: 0.4s; }
    .job-card-modern:nth-child(5) { animation-delay: 0.5s; }
    .job-card-modern:nth-child(6) { animation-delay: 0.6s; }
    .job-card-modern:nth-child(7) { animation-delay: 0.7s; }
    .job-card-modern:nth-child(8) { animation-delay: 0.8s; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Accessibility Improvements */
.job-card-modern:focus-within {
    outline: 2px solid var(--bs-primary);
    outline-offset: 2px;
}

.btn-hero-primary:focus,
.btn-hero-secondary:focus,
.btn-load-more:focus {
    outline: 2px solid var(--bs-primary);
    outline-offset: 2px;
}

/* Modern Company Page Styles */
.company-header-modern {
    position: relative;
    height: 400px;
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.company-header-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(138, 73, 98, 0.8) 0%, rgba(61, 29, 90, 0.9) 100%);
    backdrop-filter: blur(2px);
}

.company-header-content {
    position: relative;
    z-index: 2;
    color: white;
    text-align: center;
    padding: 2rem;
}

.company-logo-modern {
    width: 120px;
    height: 120px;
    margin: 0 auto 2rem;
    position: relative;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.company-logo-img {
    width: 100px;
    height: 100px;
    object-fit: contain;
    border-radius: 16px;
}

.company-logo-fallback {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
}

.company-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.company-subtitle {
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
    font-weight: 300;
}

.company-badges {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.company-badge {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.company-badge:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
}

.company-content-wrapper {
    background: white;
    border-radius: 20px 20px 0 0;
    margin-top: -40px;
    position: relative;
    z-index: 3;
    padding: 3rem 2rem;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

/* Info Cards Section */
.company-info-section {
    margin-bottom: 4rem;
}

.info-card {
    background: white;
    border-radius: 16px;
    padding: 0;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;
}

.info-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--bs-primary);
}

.info-card-header {
    background: linear-gradient(135deg, var(--bs-primary-bg) 0%, var(--bs-secondary-bg) 100%);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 1px solid #f0f0f0;
}

.info-card-header i {
    font-size: 1.5rem;
    color: var(--bs-primary);
}

.info-card-header h5 {
    margin: 0;
    font-weight: 600;
    color: var(--bs-primary);
}

.info-card-body {
    padding: 1.5rem;
}

/* Contact Card Styles */
.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 12px;
    background: #f8f9fa;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.contact-item:hover {
    background: var(--bs-primary-bg-subtle);
    transform: translateX(4px);
}

.contact-item:last-child {
    margin-bottom: 0;
}

.contact-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: var(--bs-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.contact-label {
    font-size: 0.875rem;
    color: #666;
    font-weight: 500;
}

.contact-value {
    font-weight: 600;
    color: #333;
}

/* Social Media Card Styles */
.social-link-modern {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 12px;
    background: #f8f9fa;
    margin-bottom: 1rem;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    position: relative;
}

.social-link-modern:hover {
    background: var(--bs-primary-bg-subtle);
    color: var(--bs-primary);
    transform: translateX(4px);
    text-decoration: none;
}

.social-link-modern:last-child {
    margin-bottom: 0;
}

.social-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: var(--bs-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
}

.social-name {
    flex: 1;
    font-weight: 600;
}

.social-arrow {
    opacity: 0;
    transition: all 0.3s ease;
}

.social-link-modern:hover .social-arrow {
    opacity: 1;
    transform: translateX(4px);
}

/* Location Card Styles */
.location-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 12px;
    background: #f8f9fa;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.location-item:hover {
    background: var(--bs-primary-bg-subtle);
    transform: translateX(4px);
}

.location-item:last-child {
    margin-bottom: 0;
}

.location-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: var(--bs-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
}

.location-name {
    font-weight: 600;
    color: #333;
}

/* No Info Messages */
.no-contact-info,
.no-social-info,
.no-location-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem;
    text-align: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 12px;
    font-style: italic;
}

/* Section Headers */
.section-header {
    margin-bottom: 2.5rem;
}

.section-title {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.section-title i {
    color: var(--bs-primary);
}

.section-divider {
    height: 3px;
    background: linear-gradient(90deg, var(--bs-primary) 0%, var(--bs-secondary) 50%, transparent 100%);
    border-radius: 2px;
    width: 100px;
}

.job-count-badge {
    background: var(--bs-primary);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-left: 1rem;
}

/* About Section */
.company-about-section {
    margin-bottom: 4rem;
}

.about-content {
    background: white;
    border-radius: 16px;
    padding: 2.5rem;
    border: 1px solid #f0f0f0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.about-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #555;
    text-align: justify;
}

/* Gallery Section */
.company-gallery-section {
    margin-bottom: 4rem;
}

.gallery-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.gallery-item-modern {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    aspect-ratio: 16/10;
    cursor: pointer;
    transition: all 0.3s ease;
}

.gallery-item-modern:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.gallery-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.gallery-overlay i {
    font-size: 2rem;
    color: white;
}

.gallery-item-modern:hover .gallery-overlay {
    opacity: 1;
}

.gallery-item-modern:hover .gallery-image {
    transform: scale(1.05);
}

.btn-modern {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Jobs Section */
.company-jobs-section {
    margin-bottom: 4rem;
}

.jobs-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.job-card-company {
    background: white;
    border-radius: 16px;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;
}

.job-card-company:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--bs-primary);
}

.job-card-link {
    text-decoration: none;
    color: inherit;
    display: block;
    height: 100%;
}

.job-card-link:hover {
    text-decoration: none;
    color: inherit;
}

.job-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 1.5rem 1rem;
}

.job-company-logo {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #eee;
    overflow: hidden;
}

.job-company-logo .company-logo-img {
    width: 40px;
    height: 40px;
    object-fit: contain;
    border-radius: 8px;
}

.job-company-logo .company-logo-fallback {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
}

.job-meta {
    text-align: right;
}

.job-posted {
    font-size: 0.875rem;
    color: #666;
    font-weight: 500;
}

.job-card-body {
    padding: 0 1.5rem 1rem;
}

.job-card-body .job-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.job-card-body .job-company {
    color: #666;
    margin-bottom: 1rem;
    font-weight: 500;
}

.job-tags-modern {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.job-card-footer {
    padding: 1rem 1.5rem 1.5rem;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

.salary-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.salary-amount {
    font-weight: 700;
    color: var(--bs-primary);
    font-size: 1.1rem;
}

.salary-period {
    font-size: 0.875rem;
    color: #666;
}

.salary-competitive {
    font-weight: 600;
    color: #666;
}

.apply-arrow {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--bs-primary-bg-subtle);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bs-primary);
    transition: all 0.3s ease;
}

.job-card-company:hover .apply-arrow {
    background: var(--bs-primary);
    color: white;
    transform: translateX(4px);
}

/* No Jobs Message */
.no-jobs-message {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 16px;
    border: 1px solid #f0f0f0;
}

.no-jobs-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--bs-primary-bg-subtle);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: var(--bs-primary);
    font-size: 2rem;
}

.no-jobs-message h4 {
    color: #333;
    margin-bottom: 1rem;
    font-weight: 600;
}

.no-jobs-message p {
    margin-bottom: 2rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .company-header-modern {
        height: 300px;
        background-attachment: scroll;
    }

    .company-title {
        font-size: 2rem;
    }

    .company-content-wrapper {
        margin-top: -20px;
        padding: 2rem 1rem;
    }

    .gallery-grid-modern {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }

    .jobs-grid-modern {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .company-badges {
        flex-direction: column;
        align-items: center;
    }
}

/* About Page Styles */
.about-hero-section {
    position: relative;
    height: 400px;
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.about-hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(138, 73, 98, 0.9) 0%, rgba(61, 29, 90, 0.9) 100%);
}

.about-hero-content {
    position: relative;
    z-index: 2;
    color: white;
    text-align: center;
    padding: 2rem;
}

.about-hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.about-hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    font-weight: 300;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.about-hero-badge {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

/* Mission Section */
.about-mission-section {
    padding: 5rem 0;
    background: #f8f9fa;
}

.mission-content {
    padding-right: 2rem;
}

.mission-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #555;
    margin-bottom: 1.5rem;
}

.mission-visual {
    position: relative;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mission-card {
    position: absolute;
    width: 120px;
    height: 120px;
    background: white;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
}

.mission-card i {
    font-size: 2rem;
    color: var(--bs-primary);
}

.mission-card span {
    font-weight: 600;
    font-size: 0.875rem;
    text-align: center;
    color: #333;
}

.mission-card-1 {
    top: 50px;
    left: 0;
    animation: float 3s ease-in-out infinite;
}

.mission-card-2 {
    top: 140px;
    left: 50%;
    transform: translateX(-50%);
    animation: float 3s ease-in-out infinite 1s;
}

.mission-card-3 {
    top: 50px;
    right: 0;
    animation: float 3s ease-in-out infinite 2s;
}

.mission-connection {
    position: absolute;
    height: 2px;
    background: linear-gradient(90deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
    border-radius: 1px;
}

.mission-connection-1 {
    top: 110px;
    left: 120px;
    width: 80px;
    transform: rotate(-30deg);
}

.mission-connection-2 {
    top: 110px;
    right: 120px;
    width: 80px;
    transform: rotate(30deg);
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Values Section */
.about-values-section {
    padding: 5rem 0;
    background: white;
}

.value-card {
    background: white;
    border-radius: 16px;
    padding: 2.5rem 2rem;
    text-align: center;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    height: 100%;
}

.value-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
    border-color: var(--bs-primary);
}

.value-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--bs-primary-bg-subtle) 0%, var(--bs-secondary-bg-subtle) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.value-icon i {
    font-size: 2rem;
    color: var(--bs-primary);
}

.value-card:hover .value-icon {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
}

.value-card:hover .value-icon i {
    color: white;
}

.value-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.value-description {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

/* Stats Section */
.about-stats-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
    color: white;
}

.stat-item-about {
    padding: 2rem 1rem;
}

.stat-number-about {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label-about {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 500;
}

/* Contact Section */
.about-contact-section {
    padding: 5rem 0;
    background: #f8f9fa;
}

.contact-card-modern {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
}

.contact-header {
    text-align: center;
    margin-bottom: 3rem;
}

.contact-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-title i {
    color: var(--bs-primary);
}

.contact-subtitle {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
}

.contact-method-modern {
    background: #f8f9fa;
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
}

.contact-method-modern:hover {
    background: var(--bs-primary-bg-subtle);
    transform: translateY(-4px);
}

.contact-method-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--bs-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 1.5rem;
}

.contact-method-details h5 {
    font-weight: 700;
    color: #333;
    margin-bottom: 0.5rem;
}

.contact-method-details p {
    color: #666;
    margin-bottom: 1rem;
    font-weight: 600;
}

.contact-link {
    color: var(--bs-primary);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.contact-link:hover {
    color: var(--bs-primary-bg-dark);
    text-decoration: none;
}

.contact-info {
    color: #999;
    font-size: 0.875rem;
}

.contact-cta {
    text-align: center;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #f0f0f0;
}

.cta-text {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 2rem;
    font-weight: 500;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive Design for About Page */
@media (max-width: 768px) {
    .about-hero-title {
        font-size: 2.5rem;
    }

    .about-hero-subtitle {
        font-size: 1.1rem;
    }

    .mission-content {
        padding-right: 0;
        margin-bottom: 3rem;
    }

    .mission-visual {
        height: 300px;
    }

    .mission-card {
        width: 100px;
        height: 100px;
    }

    .mission-card i {
        font-size: 1.5rem;
    }

    .mission-card span {
        font-size: 0.75rem;
    }

    .contact-card-modern {
        padding: 2rem 1.5rem;
    }

    .contact-title {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-buttons .btn {
        width: 100%;
        max-width: 300px;
    }
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
    .modern-hero-bg {
        background: linear-gradient(135deg, #222035 0%, #231c32 50%, #4a3c58 100%);
    }

    .hero-title {
        color: #ecf0f1;
    }

    .hero-subtitle {
        color: #bdc3c7;
    }

    .stat-label {
        color: #95a5a6;
    }
}