"""
Database table creation script for new features.
Run this script to create the required tables for RSS caching and job alerts.
"""

import os
import psycopg2
from dotenv import load_dotenv

load_dotenv()

def create_tables():
    """Create the new tables required for the enhanced features."""
    
    # Database connection
    conn = psycopg2.connect(dsn=os.getenv("DATABASE_URL"))
    cur = conn.cursor()
    
    try:
        # Create RSS feed cache table
        cur.execute("""
            CREATE TABLE IF NOT EXISTS rss_feed_cache (
                id SERIAL PRIMARY KEY,
                vacancy_id VARCHAR(255) UNIQUE NOT NULL,
                vacancy_title VARCHAR(500) NOT NULL,
                employer_name VARCHAR(255) NOT NULL,
                vacancy_job_description TEXT,
                vacancy_url VARCHAR(1000),
                company_url VARCHAR(1000),
                vacancy_creation_date TIMESTAMP,
                vacancy_country VARCHAR(100),
                vacancy_city VARCHAR(100),
                employer_logo_url VARCHAR(1000),
                salary_min VARCHAR(50),
                salary_max VARCHAR(50),
                salary_currency VARCHAR(10),
                vacancy_type VARCHAR(100),
                office_schedule VARCHAR(100),
                listing_source VARCHAR(100) NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        # Create job alerts table
        cur.execute("""
            CREATE TABLE IF NOT EXISTS job_alerts (
                id SERIAL PRIMARY KEY,
                email VARCHAR(255) NOT NULL,
                keywords VARCHAR(500),
                location VARCHAR(255),
                remote_only BOOLEAN DEFAULT FALSE,
                salary_min INTEGER,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_notification_sent TIMESTAMP
            );
        """)
        
        # Create job notifications table
        cur.execute("""
            CREATE TABLE IF NOT EXISTS job_notifications (
                id SERIAL PRIMARY KEY,
                alert_id INTEGER REFERENCES job_alerts(id) ON DELETE CASCADE,
                vacancy_id VARCHAR(255) NOT NULL,
                sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        # Create indexes for better performance
        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_rss_feed_cache_active 
            ON rss_feed_cache(is_active);
        """)
        
        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_rss_feed_cache_source 
            ON rss_feed_cache(listing_source);
        """)
        
        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_rss_feed_cache_created 
            ON rss_feed_cache(created_at);
        """)
        
        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_job_alerts_email 
            ON job_alerts(email);
        """)
        
        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_job_alerts_active 
            ON job_alerts(is_active);
        """)
        
        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_job_notifications_alert 
            ON job_notifications(alert_id);
        """)
        
        # Create trigger to update updated_at timestamp
        cur.execute("""
            CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = CURRENT_TIMESTAMP;
                RETURN NEW;
            END;
            $$ language 'plpgsql';
        """)
        
        cur.execute("""
            DROP TRIGGER IF EXISTS update_rss_feed_cache_updated_at ON rss_feed_cache;
            CREATE TRIGGER update_rss_feed_cache_updated_at 
                BEFORE UPDATE ON rss_feed_cache 
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        """)
        
        cur.execute("""
            DROP TRIGGER IF EXISTS update_job_alerts_updated_at ON job_alerts;
            CREATE TRIGGER update_job_alerts_updated_at 
                BEFORE UPDATE ON job_alerts 
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        """)
        
        # Commit changes
        conn.commit()
        print("✅ All tables created successfully!")
        
        # Print table information
        cur.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('rss_feed_cache', 'job_alerts', 'job_notifications')
            ORDER BY table_name;
        """)
        
        tables = cur.fetchall()
        print(f"📊 Created {len(tables)} new tables:")
        for table in tables:
            print(f"   - {table[0]}")
            
    except Exception as e:
        print(f"❌ Error creating tables: {str(e)}")
        conn.rollback()
        
    finally:
        cur.close()
        conn.close()

if __name__ == "__main__":
    print("🚀 Creating database tables for enhanced job portal features...")
    create_tables()
    print("✨ Database setup complete!")
